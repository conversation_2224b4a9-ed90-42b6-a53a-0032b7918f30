<?php
// Auto-generated blog post
// Source: content\journal\go-crash.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'GO Crash - September 17th, 2017';
$meta_description = 'Crashing After Lunch at GO Green Kitchen';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'GO Crash - September 17th, 2017',
  'author' => 'A. A. Chips',
  'date' => '2017-09-17',
  'excerpt' => 'Crashing After Lunch at GO Green Kitchen',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\go-crash.md',
);

// Post content
$post_content = '<p><img src="../../img/self/greenopportunities.jpg" alt="Green Opportunities Southside Kitchen Prep Team" width="250"></p>
<p>The lunch rush at GO Green Kitchen was a familiar storm—a whirl of shouted orders, clattering pans, and the sharp scent of searing vegetables. I moved through it like a ghost, slipping between stations, checking temps, nudging waste bins into place before they overflowed. This was my role: sanitation and systems, the invisible hand keeping the machine from choking on its own chaos. I liked the work. It was precise, necessary. But today, like every day, the rush was just the prelude to the real battle.</p>
<p>By 2 PM, the dining room had emptied. The kitchen exhaled, slumping into that post-lunch lull where time stretched like taffy. My body, though, didn’t get the memo. Adrenaline curdled into exhaustion, and the cracks in my focus widened. The fluorescent lights buzzed like wasps. The dishwasher’s steam clung to my skin. Every clang of a pot was a nail in my skull.</p>
<p>I needed to lie down. Just fifteen minutes—enough to reset the static in my brain. But this wasn’t that kind of program. We were here to train, to grind, to prove we could hack it in "real" kitchens. So we stood around, pretending to look busy while the older guys held court, swapping stories I’d heard three times already. One of them clapped me on the shoulder, called me "boss," and launched into another lecture about knife grips. I smiled tightly. They treated me like one of the boys—all macho camaraderie and assumed ignorance—even though I’d been managing kitchens since they were still burning grilled cheese.</p>
<p>The irony wasn’t lost on me. In five months, I’d file the paperwork to change my name and gender marker to female. But for now, I was trapped in their version of me: a reluctant participant in a performance I hadn’t auditioned for.</p>
<p>When the pressure in my chest threatened to crack my ribs, I bolted for the bathroom. Locked the stall. Screamed into my hands until my throat burned. Meltdowns, for me, were never messy in the moment—just a silent detonation, all the shrapnel contained. By the time I splashed water on my face and walked back out, no one had noticed I was gone.</p>
<p>Later, my case manager pulled me aside. "Maybe this isn’t the right fit," she said, her voice a careful neutral. I knew what she saw: a liability, a shaky investment for a program that promised to mold us into unbreakable kitchen soldiers. What she didn’t see was the calculus behind my endurance—how I’d survived grade school with a 400-page binder of accommodations, how living in my car was, absurdly, the healthier choice. How I could run circles around most of these guys if my body would just cooperate.</p>
<p>I talked her down. Bargained for weekly therapy sessions I didn’t want. Nodded along as the counselor fed me affirmations like "progress isn’t linear" and "honor your limits." (I preferred my own mantra: _Just get the certificate._)</p>
<p>Five months later, I graduated with excellence. The paperwork had my new name. The kitchen hadn’t changed, but I had—stitched together, somehow, by sheer stubbornness and the fragile hope that somewhere, there was a place for people who worked like me: in bursts, in shadows, in defiance of every clock that said I shouldn’t belong.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>