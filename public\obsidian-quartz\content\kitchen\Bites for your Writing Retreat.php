<?php
// Auto-generated blog post
// Source: content\kitchen\Bites for your Writing Retreat.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Fueling Inspiration: Delicious Bites for Your Writing Retreat! Ready to embark on a writing adventure with a culinary twist? This retreat isn\'t just a...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Fueling Inspiration: Delicious Bites for Your Writing Retreat! Ready to embark on a writing adventure with a culinary twist? This retreat isn\'t just a...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kitchen\\Bites for your Writing Retreat.md',
);

// Post content
$post_content = '<h2><strong>Fueling Inspiration: Delicious Bites for Your Writing Retreat!</strong></h2>
<p>Ready to embark on a writing adventure with a culinary twist? This retreat isn\'t just about words on paper, it\'s about immersing yourselves in a symphony of flavors that will tantalize your taste buds and ignite your creativity!</p>
<h2><strong>Thursday:</strong></h2>
<p>- 5:30 PM: Welcome aromas greet you as you arrive, setting the scene for a delicious retreat. Settle in with a five-course feast, a delightful tapestry of local, seasonal ingredients woven into a culinary masterpiece. Think fresh spring greens with edible flowers, a creamy butternut squash soup, and cedar plank-smoked tofu steaks with roasted vegetables and wild rice. Prepare to be wowed!</p>

<p>- Icebreaker & Introductions: After indulging in this culinary journey, ease into the evening with an engaging icebreaker and introductions. Let the laughter and connections flow, fueled by a light and satisfying dessert.</p>
<h2><strong>Friday & Saturday Mornings:</strong></h2>
<p>- ## <strong>Rise and shine with breakfast options to suit your cravings:</strong>
    
    - Oatmeal Bar Extravaganza: A DIY station bursting with rolled oats, nuts, seeds, fresh and dried fruits, spices, and plant-based milks awaits. Create your own perfect bowl to fuel your morning writing session.</p>

<p>- Savory Southern Comfort: Another day brings creamy and cheesy corn grits, a hearty Southern classic to warm your soul and awaken your senses.</p>

<p>- Brunch Bonanza: Indulge in the final morning with a leisurely brunch spread. Think fluffy pancakes or waffles, fresh fruit, yogurt parfaits, and savory options like breakfast burritos or quiche.</p>
<h2><strong>Lunchtime Delights:</strong></h2>
<p>- Friday: Feeling creative? Grab your favorite toppings and whip up a customized sandwich masterpiece. Fresh bread, savory deli meats, cheeses, veggies, and spreads will be your culinary canvas.
    
- Saturday: Take a taste bud trip with a fiesta of rice and bean wraps. Choose from black beans, pinto beans, and kidney beans, and customize your wrap with fresh veggies, salsa, and vegan cheese.</p>
<h2><strong>Dietary Delights:</strong></h2>
<p>Rest assured, this culinary adventure caters to all! Vegetarian and gluten-free options will be readily available throughout the retreat, ensuring everyone enjoys the delicious journey.</p>
<p>Join us in this immersive experience where words and flavors dance together, igniting your creativity and nourishing your soul!</p>
<p>Please note: This is just a sample proposal, and you can customize it further with additional details like specific menu items, local ingredients you plan to use, and any dietary restrictions you\'re aware of.</p>
<p>Feel free to use this information to create a Facebook Event Discussion Post/Announcement! Remember to include engaging visuals and a call to action, inviting your writing retreat participants to join the culinary adventure.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>