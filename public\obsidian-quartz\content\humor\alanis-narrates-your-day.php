<?php
// Auto-generated blog post
// Source: content\humor\alanis-narrates-your-day.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '<PERSON><PERSON> Narrates Your Day';
$meta_description = 'So don’t be surprised if you find something else to eat... but seriously, does anyone else like pimple popping videos or is it just me? Stay tuned for our behind-the-scenes making of the video!';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Alanis Morissette Narrates Your Day',
  'author' => 'Holderness Family',
  'date' => '2023-01-11',
  'excerpt' => 'So don’t be surprised if you find something else to eat... but seriously, does anyone else like pimple popping videos or is it just me? Stay tuned for our behind-the-scenes making of the video!',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\humor\\alanis-narrates-your-day.md',
);

// Post content
$post_content = '<p>---</p>
<p><a href="https://www.youtube.com/watch?v=0smCdKsvmg0" class="external-link">Alanis Morissette Narrates Your Day - YouTube</a></p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/0smCdKsvmg0?si=c1BBgedzwvNec7cs" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
<a href="https://www.youtube.com/@theholdernessfamily" class="external-link">Holderness Family Music</a></p>
<p>818K subscribers
573,039 views Jan 11, 2023 #alanismorisette #parody #comedy</p>
<p>So don’t be surprised if you find something else to eat... but seriously, does anyone else like pimple popping videos or is it just me? Stay tuned for our behind-the-scenes making of the video! #parody #comedy #alanismorisette</p>
<p>Thanks for being here! We’re Kim and Penn Holderness of The Holderness Family. We create original music, song parodies, and skits to poke fun at ourselves, the world we live in, and (hopefully) make you laugh.</p>
<p>Shop our merch: shopholderness.com
Learn about our book: theholdernessfamily.com/book
Listen to the podcast: theholdernessfamily.com/podcast</p>
<p>Subscribe to Holderness Family Music on YouTube: /theholdernessfamily
Laugh with us on Holderness Family Vlogs: /holdernessfamilyvlogs</p>
<p>Follow us on your favorite channel!
TikTok: /theholdernessfamily
Facebook: /theholdernessfamily
YouTube: /theholdernessfamily</p>
<p>Our viewers have become our family, and we try our best to respond to comments. XO</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>