<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\My favorite Spanish Songs right now.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'My favorite Spanish Songs right now  Hoy Comemos y Bebemos https://www.youtube.com/watch?v=bWG1KcwIwhU This is a very old Spanish song rooted in the 1...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'My favorite Spanish Songs right now  Hoy Comemos y Bebemos https://www.youtube.com/watch?v=bWG1KcwIwhU This is a very old Spanish song rooted in the 1...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\My favorite Spanish Songs right now.md',
);

// Post content
$post_content = '<h2>My favorite Spanish Songs right now</h2>
<h3>Hoy Comemos y Bebemos</h3>
https://www.youtube.com/watch?v=bWG1KcwIwhU
This is a very old Spanish song rooted in the 15th and 16th century of Spain. It\'s a song that would be sung before or during a great feast, and was often made into a kids song. The vocabulary is far beyond the scope of the course, but you will likely recognize the verb tenses used throughout. I have no idea if this song is rooted in Judeo-Spanish culture originally, but it is a song many expulsos took with them after leaving the Iberian Peninsula. It is a song that many conversos, or people who were forced to convert to Christianity, added their cultural flare to while possibly practicing their identity in private. It\'s really catchy!
<h3>Kanten las Sardelas!</h3>
https://www.youtube.com/watch?v=hNeRsCEBfd8&t=868s
This is a really silly song. The title translates to \'Count the Sardines!\' It is in Ladino, or Judeo- Espa&ntilde;ol. 8:40 to 10:35 (or start at 7:45 if you want to hear a prologue in Ladino). One of the main chorus lines \'Kanten las Sardelas, ensalada limoni..\' Lemon Salad is common in Spain and the Mediterranean. Maybe you might have heard of Ceviche. Ceviche is a Spanish style of cooking seafood, without heat, with use of citrus. So what a lemon salad with sardines might look like is Lemon slices, olive oil, some vinegar, and other herbs, that the sardines have been able to \'cook\' in. Yum!
<h3>Si Tienes Fe - El Principe de Egipto</h3>
https://www.youtube.com/watch?v=405i5qSGUmI
Some students in this course might not have been born yet, when Dreamworks <em>The Prince of Egypt</em> came out in 1999. It is a stunning animated film about the Jewish Exodus out of Egypt. While there are some religious references and connotations, the film is an action packed masterpiece of animation and storytelling. The movie has been made into a Spanish version as well El Principe de Egipto. It\'s a really wonderful song. If you are normally against watching forms of media that are based around Religion, give this one a shot. The movie isn\'t preachy, but it tells a story that is important to the Jewish history in a really beautiful way. The English version can be found under the name \'When you Believe,\' and was sung in the original by Mariah Carey and Whitney Houston. There is a childrens\' prayer in Hebrew halfway through the song. Hebrew is a language that shares some common words with Spanish. For example, in Hebrew, <em>a va</em> means \'is coming.\' In Spanish, va means he/she/they go(ir).
<h3>Dondequiera que Est&eacute;n - Steven Universe</h3>
https://www.youtube.com/watch?v=8wXKsA7B5NQ
From Cartoon Networks\' Steven Universe. The cartoon has been dubbed and the audio recast in Spanish through Cartoon Network Latin America. \'Be Wherever you Are.\' The whole show is full of amazing songs, which can be found for free on Youtube in both English and Spanish. Steven Universe is written by Rebecca Sugar, who also wrote the cartoon Adventure Time, which has a great soundtrack as well, including the following song.
<h3>Todo se Queda - Hora de Aventura</h3>
https://www.youtube.com/watch?v=yCveWlBieI8
This is a lullaby from the cartoon Adventure Time, this version sings both in Spanish and English. I\'ve taught this song to small kids I have babysat for and it is a really pretty ear worm. If you want to cry for days, watch the story of Marcelline and her Mother. Unfortunately I can only find it in English. https://www.youtube.com/watch?v=2Shvv19rtn4
<h3>Mi Mia Hija</h3>
https://www.youtube.com/watch?v=XAPzqAAvcTc
This is a really old Sephardic/Ladino song from Spain. It can be sung as a kids song. In the title it is referred to as \'Andalo-Sefardic.\' Andalo refers to Al-Andalus, which was the name of what we know today as Spain, between the years of 711-1492 AD.
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>