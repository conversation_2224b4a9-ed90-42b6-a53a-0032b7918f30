<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\arvoles 1.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Arvoles - Song in Ladino / Judeo Spanish This is a song in Jewish Spanish that was sung in the Italian camps called Arvoles arbol = tree. The song ori...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Arvoles - Song in Ladino / Judeo Spanish This is a song in Jewish Spanish that was sung in the Italian camps called Arvoles arbol = tree. The song ori...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\arvoles 1.md',
);

// Post content
$post_content = '<h2>Arvoles - Song in Ladino / Judeo Spanish</h2>
<p>This is a song in Jewish Spanish that was sung in the Italian camps called Arvoles (arbol = tree). The song originated from the Spanish Inquisition. Which was a genocide the Jewish and Muslims faced together at the hands of the Catholic Church in Spain in the 1400s. It was a century of brutal torturous and baron like persecution to rob the wealth of anyone who was not Catholic. They even came after reform Christians like the Protestants. Inquisition means asking of questions. They would arrest people indefinitely without charges \'just to talk\'. When we went to beach city one time we went to the Ripley\'s Museum, and there were exhibits of really old torture equipment. Those were from the Inquisition. There was nearly a century of lead up to what was called the Edict of Expulsion. This video references it at the beginning. It was a genocidal eviction at the ultimatum of \'convert, die, or leave.\' That edict of expulsion was issued from four months from then on March 31st, 1492.</p>
<p>Do the math on that date. Add four months, and five hundred years. And it\'s my birthday. My birthday, the day I was born. Was the five hundred year date of one of the bloodiest evictions, literally exiling every heretic off the Iberian Peninsula by threat of execution. Stealing every morsel of wealth they had accumulated generationally over sometimes more than 800 years of Enlightenment in Al-Andalusian rule of Spain.</p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/eht5qMyH5M8?si=HSmJrpZ9z9xRS_Xe" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<p><a href="https://www.youtube.com/watch?v=eht5qMyH5M8" target="_blank">https://www.youtube.com/watch?v=eht5qMyH5M8</a></p>
<h3>Ladino: </h3>
Arvolés Yoran Por Luvias<br>
Arvolés yoran por luvias<br>
I muntanyas por ayres.<br>
Ansí yoran los mis ojos<br>
Por ti, kerid\' amante.<br>
<p>Torno i digo ke va ser de mí.<br>
En tierras ajenas yo me vo morir.<br></p>
<h3>English Translation: </h3>
The trees cry for the rain<br>
The trees cry for the rain<br>
and the mountains cry for the wind,<br>
this is why my eyes cry;<br>
they cry for you dear beloved.<br>
<p>I return and say: "what will become of me,<br>
I\'ll die in a foreign land".<br></p>
<p>https://lyricstranslate.com</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>