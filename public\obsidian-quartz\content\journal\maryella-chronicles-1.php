<?php
// Auto-generated blog post
// Source: content\journal\maryella-chronicles-1.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Maryella Chronicles Part 1';
$meta_description = 'This is the first part of the Maryella Chronicles. It\'s a work in progress. I\'m not sure how many parts there will be. I\'m just going to write as I go.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Maryella Chronicles Part 1',
  'author' => 'A. A. Chips',
  'date' => '2018-02-02',
  'excerpt' => 'This is the first part of the Maryella Chronicles. It\'s a work in progress. I\'m not sure how many parts there will be. I\'m just going to write as I go.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\maryella-chronicles-1.md',
);

// Post content
$post_content = '<p>After a year of calling my vehicle home, 12 Baskets became my sanctuary. They offered a safe haven and good food, literally saving my life. I was a "bum" until just a year ago, now trying to mind my own business.</p>
<p>One day, a girl named Robin approached me, asking what I was doing. "Watching YouTube," I replied, then offered to let her watch cartoons with me. Soon, she and her sister, Raven, were showing me their favorites. It brought back memories of my niece, a child I’d acted as a surrogate parent to before alienating myself from my family.</p>
<p>Weeks passed, and I kept seeing Robin and Raven at 12 Baskets. "Why aren\'t these kids in school?" I wondered. I wanted to know their story, promising to keep a secret if they were on the run. I believe in supporting mothers and fathers, and this wasn’t a secret I’d keep hidden. If a community\'s vision doesn\'t include struggling families, I want no part of it. This conviction has backfired, leaving me hurt, but I\'m also working on my own issues with intimacy, which I find terrifying and often evade.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>