<?php
// Auto-generated blog post
// Source: content\fish-in-water-media.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Two fish are swimming in the ocean. One fish asks to the other fish, "How\'s the water?" The other fish replies, "What is water..?" The media environme...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Two fish are swimming in the ocean. One fish asks to the other fish, "How\'s the water?" The other fish replies, "What is water..?" The media environme...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\fish-in-water-media.md',
);

// Post content
$post_content = '<p>Two fish are swimming in the ocean. One fish asks to the other fish, "How\'s the water?" The other fish replies, "What is water..?"</p>
<p>The media environment we are immersed in, day-in, and day-out, becomes so normalized, that it can become invisible. How many media messages do we consume each day? From television, advertisements, internet, waking life, and in our daily social interactions with others immersed? And who is it that chooses these messages?</p>
<p>I watched a video essay today, which compared children\'s movies from Disney, and Studio Ghibli. They pointed out that Disney movies are full of moral messages about being kind, but they always portray the greater world as hostile and dangerous, while movies from Studio Ghibli break out of this and show a kinder world. They mention how many of us are more apt to accept seeing a doomsday landscape of cruelty in a movie like Madmax, than accept a movie that entertains the world can be kind and full of love and acceptance. The thing is that, I am in my mid thirties,  have grown up with Disney movies, and have never considered this observation and possibility.</p>
<p>I reject the idea of free speech or the marketplace of ideas. People misuse these concepts to justify being terrible, and remove accountability for their actions. Free speech does not mean we are not responsible for what we say and do in the world (or in the United States). </p>
<p>This past month a college student with full American citizenship has faced the threat of deportation for speaking ideas that are existentially hostile to our government. I don\'t even feel safe voicing what that was about on this forum without risking facing repercussions that defy the notion of \'Academic Freedom.\' I actually hold a lot of fear in my heart of what out media landscape has been becoming for years. I\'ve lost both my parents to what are called information silos. More than once, my own government, that I pay taxes to, has targeted my kind for harassment and political vitriol. So I am very mindful about the information I consume. As a technology professional, I am also conscious about the personal information I put online, and feed to robots. </p>
<p>One of the best solutions I have seen to this broken landscape of information, is something happening in grade school curricula in Finland. In Finland, it is required for kids to take courses on Media Literacy, throughout their education. These are classes where you learn how to spot bad information, and malicious information, in print and web media. It actually is really fun. You learn how to spot lies and identify propaganda. The learning materials are open source, you can find them online. I am fairly sure the curriculum for this class is inspired in some way by Finland\'s models. Imagine if the skills you learn in this class started at a young age? </p>
<p>Self-Assessment:</p>
<p>- I seek out independent sources of information about an issue instead of relying on televised news sources. I actively curate my media experience every day of my life and push back on my own bad belief systems.
    
- As a content maker, I respect the power of media, understand how people can receive and respond to information, and have high expectations for what I consume.
    
- I find tools to help me analyze sources of information, including peer review, like in scientific research. But generally I have people I trust to be balanced to bounce news off of.
    
- However, I am not very good at reading, and rely often on second and third hand sources for the delivery of information. I can get very reactive seeing upsetting things in the news without taking the time to assess the quality of the information. </p>
<p>I won\'t score myself and pat myself on the back. There is a lot I can do to improve my media literacy.</p>
<p>A trick I have picked up in the past few months from a content creator I respect, is called the 72 hour rule. This says that if you see a news headline that is emotionally or otherwise triggering, wait 72 hours before reacting, or responding to it. We are inundated with shock content on the news to desensitize us over time, and many times in the timeframe of 72 hours, you will find an injection of sanity embedded in the story by then. Or it will be checked by other powers in the situation, or bad information will be dispelled.</p>
<p>Sources: </p>
<p>- Charlton, E. (2019, May 21). <a href="https://www.weforum.org/stories/2019/05/how-finland-is-fighting-fake-news-in-the-classroom/" class="external-link">How Finland is fighting fake news in the classroom.</a> World Economic Forum. 
    
- Marcelo, P. (2025, March 11). <a href="https://apnews.com/article/columbia-university-mahmoud-khalil-ice-440828980a4ee7bf4ddcf3d123e02b3e%20%E2%80%8C" class="external-link">Mahmoud Khalil: Who is the Columbia student activist arrested by ICE? AP News</a>. - The Associated Press (AP) is often the original source for other news outlets to find the press releases of stories. As far as news sources go, it is meant to be the least compromised and most politically neutral.
    
- The Soak. (2024, December 28). Why Ghibli Succeeds Where Disney Fails. YouTube.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>