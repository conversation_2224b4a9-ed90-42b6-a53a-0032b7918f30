<?php
// Auto-generated blog post
// Source: content\come-to-me.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'I like my space, but someone or someones need me to show up in the world more';
$meta_description = 'I value my equilibrium and independence. I love being in my space in quiet, and don\'t crave leaving, or get the dreaded FOMO. I need time alone to process emotions. I\'ve developed strong methods for maintaining my spiritual and emotional balance. I\'ve retreated from past friendships to protect my energy, also due to past experiences.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'I like my space, but someone or someones need me to show up in the world more',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'I value my equilibrium and independence. I love being in my space in quiet, and don\'t crave leaving, or get the dreaded FOMO. I need time alone to process emotions. I\'ve developed strong methods for maintaining my spiritual and emotional balance. I\'ve retreated from past friendships to protect my energy, also due to past experiences.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\come-to-me.md',
);

// Post content
$post_content = '<p>I value my equilibrium and independence. I love being in my space in quiet, and don\'t crave leaving, or get the dreaded FOMO. I need time alone to process emotions. I\'ve developed strong methods for maintaining my spiritual and emotional balance. I\'ve retreated from past friendships to protect my energy, also due to past experiences.</p>
<p>In the past I have dealt with a lot of difficult people who deliberately misunderstand me and siphon my life energy away from me. Social interactions would leave me drained and felt obligatory, that after some time I didn\'t actually want them. As a result I have walls around me, and on some level fear losing my sense of self in relationships with others.</p>
<p>I know there are people in the world who desperately need my presence in their life. I know there is likely one or a few persons in particular wanting to connect. I may have signed into this situation way before I was born, knowing then what I was getting into. I know forming new connections will be different from past relationships.</p>
<p>I\'m stable and secure in my peace, independence, and spirituality, and am not threatened by new connections. My strength and presence is needed by others in the world. This will bring mutual meaning and joy. I can maintain the leader role without being drained by old dynamics. The universe is gently guiding me towards these connections.</p>
<p>Here are my requirements for new connection from others:</p>
<p>+ Openness and committment to learning new things. Change is a constant in life and that\'s okay.
+ Say what you mean. Mean what you say.
+ Be good to others. Don\'t be a dingle.
+</p>
<p><strong>The Specific Soul Connection:</strong>
    - Someone specific needs your friendship and understanding
    - They\'re facing challenges but won\'t burden you with their problems
    - You\'ve been best friends in past lives
    - This person feels and understands you on a deep level
    - Together you\'ll create something innovative and meaningful</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>