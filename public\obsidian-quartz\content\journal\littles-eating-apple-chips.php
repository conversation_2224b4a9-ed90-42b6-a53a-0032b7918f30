<?php
// Auto-generated blog post
// Source: content\journal\littles-eating-apple-chips.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Littles Eating Apple Chips';
$meta_description = 'Today was special. One of my students had a birthday party and insisted I come. Our school has a wonderful sense of community, and it was nice to see two other teachers there as well. The kids were having a blast, and <PERSON> was so happy I could make it.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Littles Eating Apple Chips',
  'author' => 'A. A. Chips',
  'date' => '2025-05-31',
  'excerpt' => 'Today was special. One of my students had a birthday party and insisted I come. Our school has a wonderful sense of community, and it was nice to see two other teachers there as well. The kids were having a blast, and Iris was so happy I could make it.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\littles-eating-apple-chips.md',
);

// Post content
$post_content = '<h3>Seeing Little Ones Enjoy Healthy Treats > Any Paycheck (Tips Welcome!)</h3>
<p>May 31, 2025</p>
<p>Today was special. One of my students had a birthday party and insisted I come. Our school has a wonderful sense of community, and it was nice to see two other teachers there as well. The kids were having a blast, and Iris was so happy I could make it.</p>
<p>I\'ll admit, I was a little hesitant about going at first. I was concerned about professional boundaries. Was this stepping over a line? I decided to ask my boss about it. His response put me at ease. He said it was entirely up to my professional discretion, as long as I continued to represent the school well. He even joked that if I planned on getting tipsy and dancing on any bar counters, I should do it in the next district over!</p>
<p>Ultimately, I\'m really glad I went. It\'s not often I get the chance to connect with parents outside of school events. Plus, I brought along eighteen bags of post-season apple chips to share. I suggested they\'d be even better warmed up in the oven or toaster, but the kids couldn\'t wait. They gobbled them right up, even with a whole table of sugary birthday treats just a few steps away. Seeing them devour my cool, healthy apple snacks while surrounded by junk food? That\'s a feeling that\'s worth more than any paycheck. (Though, tips are still very much appreciated!)</p>
<p>![[iris2.jpg]]</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>