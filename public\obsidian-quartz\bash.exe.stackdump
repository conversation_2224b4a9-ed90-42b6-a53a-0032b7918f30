Stack trace:
Frame         Function      Args
0007FFFF8B70  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7A70) msys-2.0.dll+0x2118E
0007FFFF8B70  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF8B70  0002100469F2 (00021028DF99, 0007FFFF8A28, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8B70  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8B70  00021006A545 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF9F100000 ntdll.dll
7FFF9E670000 KERNEL32.DLL
7FFF9C250000 KERNELBASE.dll
7FFF98D70000 apphelp.dll
7FFF9E250000 USER32.dll
7FFF9CDD0000 win32u.dll
7FFF9EDD0000 GDI32.dll
7FFF9C9D0000 gdi32full.dll
7FFF9CD20000 msvcp_win.dll
7FFF9CBD0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF9EFF0000 advapi32.dll
7FFF9E8D0000 msvcrt.dll
7FFF9EF40000 sechost.dll
7FFF9E7B0000 RPCRT4.dll
7FFF9B850000 CRYPTBASE.DLL
7FFF9CE00000 bcryptPrimitives.dll
7FFF9EA80000 IMM32.DLL
