<?php
// Auto-generated blog post
// Source: content\inspiration\150-years-from-now.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '150 years from now, none of us reading this post today will be alive';
$meta_description = 'Let us take life easy, nobody will get out of this world alive. . . The land you are fighting and ready to kill for, somebody left that land, the person is dead, rotten, and forgotten. That will also be your fate. In 150 years to come, none of the vehicles or phones we are using today to brag will be relevant. Biko, take life easy!';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '150 years from now, none of us reading this post today will be alive',
  'author' => 'Claire Rose Elise',
  'date' => '2023-12-22',
  'excerpt' => 'Let us take life easy, nobody will get out of this world alive. . . The land you are fighting and ready to kill for, somebody left that land, the person is dead, rotten, and forgotten. That will also be your fate. In 150 years to come, none of the vehicles or phones we are using today to brag will be relevant. Biko, take life easy!',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\inspiration\\150-years-from-now.md',
);

// Post content
$post_content = '<p><img src="../../img/art/get-us-out.jpg" width="400" alt="Get us out of Kakuma."></p>
<p>Let love lead ❤️.</p>
<p>150 Years From Now</p>
<p>150 years from now, none of us reading this post today will be alive. 70 percent to 100 percent of everything we are fighting over right now will be totally forgotten. Underline the word, [#TOTALLY]</p>
<p>If we go back memory lane to 150 years before us, that will be 1872, none of those that carried the world on their heads then are alive today. Almost all of us reading this will find it difficult to picture anybody\'s face of that era.</p>
<p>Pause for a while and imagine how some of them betrayed their relatives and sold them as slaves for a piece of mirror. Some killed family members just for a piece of land or tubers of yam or cowries or for a pinch of salt. Where is the yam, cowries, mirror, or salt that they were using to brag? It may sound funny to us now, but that is how silly we humans are sometimes, especially when it comes to power or trying to be relevant.</p>
<p>I remember those days in my secondary school, how some people fought and did so many unimaginable things just to have their names shortlisted among those to be made school Prefects. Ordinary school Prefects o! It is just about 18 years since I left secondary school, nobody in that school right now remembers that I even schooled there despite my popularity then. Now, imagine what happens after 150 years?</p>
<p>Even when you claim the internet age will preserve your memory, take Michael Jackson as an example. Michael Jackson died in 2009, that was just 13 years ago. Imagine the influence Michael Jackson had all over the world when he was alive. Gosh, he was like a god. How many young people of today remember him with awe, that is if they even know him? In 150 years to come, his name, when mentioned, will not ring any bell to a lot of people. This is even because he was popular, imagine the majority of people who will never be known worldwide like him?</p>
<p>Let us take life easy, nobody will get out of this world alive. . . The land you are fighting and ready to kill for, somebody left that land, the person is dead, rotten, and forgotten. That will also be your fate. In 150 years to come, none of the vehicles or phones we are using today to brag will be relevant. Biko, take life easy!</p>
<p>Let love lead. Be genuinely happy for each other. No malice, no backbiting. No jealousy. No comparison. It is not a competition. At the end of the day, we all have the same destiny in the grave. It is just a question of who gets there first, but surely we will all go there.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>