<?php
// Auto-generated blog post
// Source: content\climate\humanure-safe-alternative.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Humanure 101: The Safe, Sustainable Alternative to Flushing  What If Your Poop Could Help Save the Planet? Most of us never think about what happens a...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Humanure 101: The Safe, Sustainable Alternative to Flushing  What If Your Poop Could Help Save the Planet? Most of us never think about what happens a...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\climate\\humanure-safe-alternative.md',
);

// Post content
$post_content = '<h1><strong>Humanure 101: The Safe, Sustainable Alternative to Flushing</strong></h1>
<h3><strong>What If Your Poop Could Help Save the Planet?</strong></h3>
<p>Most of us never think about what happens after we flush. But our modern sewage system—designed to make waste "disappear"—is actually <strong>wasting water, polluting ecosystems, and stripping soil of vital nutrients.</strong></p>
<p>There’s a better way: <strong>humanure</strong> (composted human waste). It’s not a new idea—farmers worldwide have used "night soil" for centuries—but with climate change and water scarcity worsening, it’s time to rethink our "flush and forget" habit.</p>
<p>---</p>
<h2><strong>How Humanure Works (And Why It’s Safe)</strong></h2>
<h3><strong>The Basics</strong></h3>
<p>Humanure is <strong>human waste composted into fertile soil.</strong> Unlike sewage, which mixes poop with chemicals and flushes it into waterways, humanure:  
✅ <strong>Uses no water</strong> (critical in droughts).  
✅ <strong>Returns nutrients to the earth</strong> (closing the waste loop).  
✅ <strong>Reduces pollution</strong> (no pharmaceuticals or microplastics in rivers).</p>
<h3><strong>Safety First: How to Compost Poop Correctly</strong></h3>
<p>Yes, composting poop safely is possible—but it requires <strong>proper handling:</strong></p>
<p>1. <strong>Separate urine & feces</strong> (urine is sterile when fresh; feces need composting).
    
2. <strong>Use a carbon-rich cover material</strong> (sawdust, straw, or leaves to balance moisture and odor).
    
3. <strong>Compost at high heat</strong> (140°F+ for months kills pathogens).
    
4. <strong>Let it age</strong> (finished compost should cure for <strong>at least a year</strong> before use).
    
5. <strong>Use on non-edible plants first</strong> (or follow strict safety guidelines for food crops).</p>
<p><strong>Myth:</strong> "Humanure is dangerous and smells bad."  
<strong>Reality:</strong> Done right, it’s <strong>odorless and safe</strong>—just like animal manure (which we already use on crops).</p>
<p>---</p>
<h2><strong>The Trade-Off: Extra Labor, Big Rewards</strong></h2>
<h3><strong>What It Takes</strong></h3>
<p>Composting toilets <strong>aren’t zero-maintenance.</strong> You’ll need to:</p>
<p>- <strong>Empty and manage waste bins</strong> (like taking out the trash, but for compost).
    
- <strong>Monitor temperature and moisture</strong> (for safe breakdown).
    
- <strong>Be patient</strong> (good compost takes time).</p>
<h3><strong>Why It’s Worth It</strong></h3>
<p>- <strong>Saves thousands of gallons of water per year.</strong>
    
- <strong>Rebuilds degraded soil</strong> (critical for fighting climate change).
    
- <strong>Reduces reliance on synthetic fertilizers</strong> (which come from fossil fuels).</p>
<p>---</p>
<h2><strong>Who’s Already Doing This?</strong></h2>
<p>- <strong>Eco-villages & off-grid homes</strong> (where sewage isn’t an option).
    
- <strong>Farmers in water-scarce regions</strong> (using compost to restore land).
    
- <strong>Forward-thinking cities</strong> (like Stockholm, which turns sewage into fertilizer).</p>
<p><strong>Even NASA is researching humanure for Mars missions</strong>—because in space, every drop of water (and every nutrient) counts.</p>
<p>---</p>
<h2><strong>Ready to Rethink Your Toilet?</strong></h2>
<p>You don’t have to go off-grid to make a difference. Start small:</p>
<p>- <strong>Try a composting toilet</strong> (camping or at a cabin).
    
- <strong>Support soil-based waste solutions</strong> in your community.
    
- <strong>Question the flush habit</strong>—because <strong>poop shouldn’t be waste. It should be a resource.</strong></p>
<p><strong>The future of sanitation isn’t just about getting rid of waste—it’s about turning it into something better.</strong></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>