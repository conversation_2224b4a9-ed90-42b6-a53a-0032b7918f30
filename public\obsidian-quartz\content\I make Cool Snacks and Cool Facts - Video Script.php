<?php
// Auto-generated blog post
// Source: content\I make Cool Snacks and Cool Facts - Video Script.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Opening Hook 0:00–0:30 - Visual: Close-up of apple chips drying or a montage of apples being sliced.      - Script:          > "Every year, millions...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Opening Hook 0:00–0:30 - Visual: Close-up of apple chips drying or a montage of apples being sliced.      - Script:          > "Every year, millions...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\I make Cool Snacks and Cool Facts - Video Script.md',
);

// Post content
$post_content = '
<p>#### <strong>Opening Hook (0:00–0:30)</strong></p>
<p>- <strong>Visual</strong>: Close-up of apple chips drying or a montage of apples being sliced.
    
- <strong>Script</strong>:
    
    > _"Every year, millions of pounds of perfectly good apples end up in landfills—while people go hungry. My name’s April, and I turned this absurd waste into a side hustle that’s now a mission: apple chips. Here’s how I went from homeless to running a food justice project... and how you can do it too."_</p>
<p>#### <strong>Part 1: The Origin Story (0:30–2:00)</strong></p>
<p>- <strong>Key Points</strong>:
    
    - Your 15-year background in food recovery/sustainability.
        
    - The 2019 church partnership (thousands of pounds of overstock, apples piling up).
        
    - Why dehydrating was the solution (vs. canning/apple sauce).
        
- <strong>Visual</strong>: Photos of the church operation, stacks of apple boxes, or you sorting food.</p>
<p>#### <strong>Part 2: The Process (2:00–4:00)</strong></p>
<p>- <strong>Key Points</strong>:
    
    - Low-barrier setup: home kitchen, borrowed dehydrators, minimal costs.
        
    - <strong>Not a recipe</strong>, but emphasize: open-source process, Creative Commons ethos.
        
    - Logistics: 1,600+ pounds processed, time/batch constraints (breaker box limits, 10–12hr dry time).
        
- <strong>Visual</strong>: Time-lapse of apples dehydrating or you prepping slices.</p>
<p>#### <strong>Part 3: Why This Matters (4:00–6:00)</strong></p>
<p>- <strong>Key Points</strong>:
    
    - Food waste + food deserts: Chips are lightweight, shelf-stable, accessible.
        
    - <strong>Cottage food laws</strong>: "Research your state’s rules—many make it easy to start small!"
        
    - <strong>Diplomatic phrasing</strong>:
        
        > _"With rising food insecurity, projects like this use existing systems—like the Emerson Act—to turn waste into community resources. No dumpster-diving needed."_
        
- <strong>Visual</strong>: Screenshare of your "Why Chip" landing page or a map of food waste stats.</p>
<p>#### <strong>Part 4: Call to Action (6:00–7:00)</strong></p>
<p>- <strong>Key Points</strong>:
    
    - <strong>For viewers</strong>: Try it in your community! Tag me with #BiteChipDIY.
        
    - <strong>Support</strong>: Free chips for locals, donations/pledges for scaling the project.
        
    - <strong>Cool Facts</strong>: Tease your website’s quirky apple trivia (screen share).
        
- <strong>Visual</strong>: Packing chips into bags or a montage of happy recipients.</p>
<p>#### <strong>Closing (7:00–end)</strong></p>
<p>- <strong>Script</strong>:
    
    > _"If you’re tired of doomscrolling and want to _do_ something, start small. Dry apples. Share soup mix. Fight waste your way. And hey—if you’ve got a wild apple fact, drop it below. I’ve got 968 more to collect."_
    
- <strong>Visual</strong>: Outro with your website/donation link on screen.</p>
<p>---</p>
<h3><strong>YouTube Metadata Template</strong></h3>
<p><strong>Title</strong>: _How I Turn Food Waste Into a Business (Apple Chips for Justice)_  
<strong>Description</strong>:</p>
<p>text</p>
<p>Copy</p>
<p>Download</p>
<p>America wastes 40% of its food—but what if you could turn that into a side hustle? Meet April’s Apple Chips: a food justice project that’s rescued 1,600+ lbs of apples from landfills. Learn how to start your own low-waste snack biz (no fancy equipment needed)!</p>
<p>🔗 [Your Website] | Donate to the project: [Link]</p>
<p>⏱️ TIMESTAMPS  
0:00 - From Homeless to Hustle  
1:30 - How I Got Thousands of Free Apples  
3:15 - Why Dehydrating Beats Canning  
5:00 - How YOU Can Do This Legally  
6:30 - Join the Movement</p>
<p>#FoodJustice #ZeroWaste #CottageBusiness #DIY</p>
<p><strong>Tags</strong>: `food waste, apple chips, cottage food laws, sustainability, social enterprise, dehydrating snacks`</p>
<p><strong>Thumbnail Idea</strong>:</p>
<p>- <strong>Text</strong>: "I Turn Trash Into Snacks!"
    
- <strong>Image</strong>: You holding apple chips with a dehydrator in the background.</p>
<p>---</p>
<h3><strong>Bonus Tips</strong></h3>
<p>1. <strong>Engagement</strong>: Pin a comment asking, _"What’s your favorite underrated snack?"_ to spark discussion.
    
2. <strong>Playlist</strong>: Add this to a "Food Justice" or "DIY Business" playlist on your channel.
    
3. <strong>Cards/End Screen</strong>: Link to your website or a related video (e.g., "How to Solicit Food Donations").</p>
<p>This keeps your message focused while weaving in your personal story, actionable advice, and community-building. Let me know if you’d like tweaks!</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>