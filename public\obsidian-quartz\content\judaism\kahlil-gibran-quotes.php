<?php
// Auto-generated blog post
// Source: content\judaism\kahlil-gibran-quotes.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Nearly 90 years after the great visionary died, allow his wise words to uplift you - <PERSON><PERSON><PERSON>';
$meta_description = '<PERSON><PERSON><PERSON> died on April 10, 1931 in New York. The Lebanese-American writer, poet and visual artist was just 48 years old. And yet, nearly 90 years later, he\'s remembered as one of the great visionaries of his time, a pivotal figure on the Arabic literature scene of the early 1900s, thanks to his romantic style and prose poetry.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Nearly 90 years after the great visionary died, allow his wise words to uplift you - Kahlil Gibran',
  'author' => 'Katy Gillett - The National News',
  'date' => '2019-04-10',
  'excerpt' => 'Kahlil Gibran died on April 10, 1931 in New York. The Lebanese-American writer, poet and visual artist was just 48 years old. And yet, nearly 90 years later, he\'s remembered as one of the great visionaries of his time, a pivotal figure on the Arabic literature scene of the early 1900s, thanks to his romantic style and prose poetry.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\judaism\\kahlil-gibran-quotes.md',
);

// Post content
$post_content = '<p><a href="https://www.thenationalnews.com/arts/10-inspiring-quotes-from-lebanese-poet-kahlil-gibran-1.847136" class="external-link">10 inspiring quotes from Lebanese poet Kahlil Gibran - The National News</a></p>

<p>!<a href="https://thenational-the-national-prod.cdn.arcpublishing.com/resizer/v2/Z2L2CW2667MOLMAGBZBHGGJUNY.jpg?smart=true&auth=518c8fb32f6beb87279050c8dcc78dd3d82cd8883335e2ec66db620728d9f06f&width=400&height=530" class="external-link">UNSPECIFIED - JANUARY 11:  Photograph by Fred Holland Day (1864-1933) of Lebanese writer Kahlil Gibran (1883-1931).  (Photo by The Royal Photographic Society Collection/Victoria and Albert Museum, London/Getty Images)</a></p>

<p>UNSPECIFIED - JANUARY 11: Photograph by Fred Holland Day (1864-1933) of Lebanese writer Kahlil Gibran (1883-1931). (Photo by The Royal Photographic Society Collection/Victoria and Albert Museum, London/Getty Images)</p>

<p><a href="https://www.thenationalnews.com/topics/Author/katy-gillett/" class="external-link">Katy Gillett</a></p>
<p>Apr 10, 2019</p>

<p>Kahlil Gibran died on April 10, 1931 in New York. The Lebanese-American writer, poet and visual artist was just 48 years old. And yet, nearly 90 years later, he\'s remembered as one of the great visionaries of his time, a pivotal figure on the Arabic literature scene of the early 1900s, thanks to his romantic style and prose poetry.</p>

<p>He\'s arguably best known for his book _The Prophet_, a collection of 26 prose poems and sermons delivered by a fictional sage, written in English and published in 1923. It has since sold millions of copies across the world, making Gibran one of the world\'s best-selling poets of all time. In fact, it is often reported that he is the third best-selling poet, after Shakespeare and Lao-tzu.</p>

<p>Gibran and his work have been the subjects of <a href="https://www.thenationalnews.com/arts-culture/art/lebanon-s-most-famous-son-why-kahlil-gibran-s-words-are-still-prophetic-today-1.756539" class="external-link">exhibitions</a>, <a href="https://www.thenationalnews.com/arts-culture/the-life-of-khalil-gibran-gets-the-documentary-treatment-1.398960" class="external-link">films</a> and other art forms for decades, including an <a href="https://www.thenationalnews.com/arts-culture/an-inspired-animated-film-brings-kahlil-gibran-s-work-to-life-in-real-style-1.113267?videoId=5688157736001" class="external-link">animated film</a> based on _The Prophet_ that was <a href="https://www.thenationalnews.com/arts-culture/a-chat-with-salma-hayek-and-the-creative-minds-behind-kahlil-gibran-s-the-prophet-ahead-of-its-middle-east-debut-1.601198?videoId=5753639305001" class="external-link">produced by Salma Hayek</a> and starred Liam Neeson.</p>

<p><iframe width="560" height="315" src="https://www.youtube.com/embed/lwQbMxDcRGE?si=IfLyNj7w6Jw8IglE" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>

<p>While his technique as a wordsmith is undeniable, it\'s the content in his writings that still resonates in the 21st century – and likely will continue to for centuries to come. He deals with timeless topics centred on the human condition, such as love, friendship, generosity and suffering.</p>

<h2>Here are just 10 of his most inspiring quotes:</h2>

<p>1. \'In the sweetness of friendship let there be laughter, and sharing of pleasures. For in the dew of little things the heart finds its morning and is refreshed.\'</p>
<p>2. \'If you love somebody, let them go, for if they return, they were always yours. And if they don\'t, they never were.\'</p>
<p>3. \'Out of suffering have emerged the strongest souls; the most massive characters are seared with scars.\'</p>
<p>4. \'Love one another, but make not a bond of love: Let it rather be a moving sea between the shores of your souls.\'</p>
<p>5. \'I have learned silence from the talkative, toleration from the intolerant, and kindness from the unkind; yet, strange, I am ungrateful to those teachers.\'</p>
<p>6. \'When you are sorrowful look again in your heart, and you shall see that in truth you are weeping for that which has been your delight.\'</p>
<p>7. \'Your living is determined not so much by what life brings to you as by the attitude you bring to life; not so much by what happens to you as by the way your mind looks at what happens.\'</p>
<p>8. \'To understand the heart and mind of a person, look not at what he has already achieved, but at what he aspires to.\'</p>
<p>9. \'If the other person injures you, you may forget the injury; but if you injure him you will always remember.\'</p>
<p>10. \'Knowledge of the self is the mother of all knowledge. So it is incumbent on me to know my self, to know it completely, to know its minutiae, its characteristics, its subtleties, and its very atoms.\'</p>
<p><img src="../../img/art/gibran-quote.jpg" width="400" alt="Kahlil Gibran quote."></p>
<p><strong>Read more:</strong></p>
<p>+ <strong><a href="https://www.thenationalnews.com/arts-culture/art/lebanon-s-most-famous-son-why-kahlil-gibran-s-words-are-still-prophetic-today-1.756539" class="external-link">Lebanon\'s most famous son: Why Kahlil Gibran\'s words are still prophetic today</a></strong></p>
<p>+ <strong><a href="https://www.thenationalnews.com/arts-culture/books/gulf-author-shortlisted-for-man-booker-international-prize-for-the-first-time-1.847085" class="external-link">Gulf author shortlisted for Man Booker International Prize for the first time</a></strong></p>
<p>+ <strong><a href="https://www.thenationalnews.com/arts-culture/five-must-read-books-that-offer-insight-into-the-middle-east-across-the-centuries-1.845233" class="external-link">Five must-read books that offer insight into the Middle East across the centuries</a></strong></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>