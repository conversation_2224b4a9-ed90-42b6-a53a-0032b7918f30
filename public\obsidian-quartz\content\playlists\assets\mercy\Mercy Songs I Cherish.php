<?php
// Auto-generated blog post
// Source: content\playlists\assets\mercy\Mercy Songs I Cherish.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Here are songs my congregation sings that I find meaningful as a Sephardic Jew.  And we pray / What we need And we pray, not for new earth or heaven, ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Here are songs my congregation sings that I find meaningful as a Sephardic Jew.  And we pray / What we need And we pray, not for new earth or heaven, ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\mercy\\Mercy Songs I Cherish.md',
);

// Post content
$post_content = '<p>Here are songs my congregation sings that I find meaningful as a Sephardic Jew.</p>
<h3>And we pray / What we need</h3>
And we pray, not for new earth or heaven,
but to be quiet in heart, and in eye clear.
<p>What we need is here.</p>
<h3>What I see</h3>
adapted from "In the heavens I see your eyes" by Rumi
<p>In your eyes I see the heavens.  
  
Why look for another Moon or another Sun?—  
What I see will always be enough for me.”</p>
<h3>Leaning on the Everlasting Arms / En los Brazos de mi Salvador</h3>
[[Leaning on the Everlasting Arms En Los Brazos de mi Salvador]]
What a fel-low-ship, 
what a joy di-vine, 
Lean-ing on the ev-er-last-ing arms, 
What a bless-ed-ness, what a peace is mine, 
Lean-ing on the ev-er-last-ing arms. 
Lean-ing, lean-ing, Safe and se-cure from all a-larms, 
Lean-ing, lean-ing, lean-ing on the ev-er-last-ing arms. 
Cuán dulce es vivir, cuán dulce es gozar 
En los brazos de mi Salvador!
<p>Allí quiero ir y con él morar, 
Siendo objeto de su tierno amor.</p>
<p>Libre, salvo del pecado y del temor, 
Libre, salvo, en los brazos de mi Salvador.</p>
<p>What have I to dread, 
what have I to fear, 
Lean-ing on the ev-er-last-ing arms? 
I have bless-ed peace with my Lord so near, 
Lean-ing on the ev-er-last-ing arms. 
Lean-ing, lean-ing, Safe and se-cure from all a-larms, 
Lean-ing, lean-ing, lean-ing on the ev-er-last-ing arms.</p>
<h3>Wade in the water</h3>
African American Spiritual
<p>Wade in the water, wade in the water, children,
Wade in the water; God\'s a gonna trouble the water</p>
<h3>Hamba Nathi</h3>
Come walk with us, the journey is long
The journey, the journey, the journey is long
<p>Come eat with us, and share in our bread....
Share our burden, and join in the song....</p>
<h2>Vine and Fig Tree</h2>
And ev\'ryone beneath the vine and fig tree  
shall live in peace and have no fear.  
And into plough shares turn their swords,  
nations shall learn war no more.  
Love to your neighbor, and
love to the spirit of all life.
<h2>The tree of the field (will clap their hands)</h2>
You shall go out with joy and be led forth with peace,  
And the mountains and the hills will break forth before you.  
There\'ll be shouts of joy and the trees of the fields  
Will clap, will clap their hands.  
  
And the trees of the fields will clap their hands,  
And the trees of the fields will clap their hands,  
And the trees of the fields will clap their hands,  
While you go out with joy.
<h2>Keep your Lamps</h2>
Keep your lamps trimmed and burning, the time is drawing nigh.
People don\'t get weary till your work is done.
<h2>Holy Child of God</h2>
I see the love of god in you,
the light of christ comes shining through
and I am blessed to be with you
o holy child of god
<h2>Longing for light, we wait in darkness</h2>
<p>Longing For Light, We Wait In Darkness.  
Longing For Truth, We Turn To You.  
Make Us Your Own, Your Holy People,  
Light For The World To See.</p>
<p>Christ, Be Our Light!  
Shine In Our Hearts.  
Shine Through The Darkness.  
Christ, Be Our Light!  
Shine In Your Church Gathered Today.</p>
<p>Longing For Peace, Our World Is Troubled.  
Longing For Hope, Many Despair.  
Your Word Alone Has Power To Save Us.  
Make Us Your Living Voice.</p>
<p>Christ, Be Our Light!  
Shine In Our Hearts.  
Shine Through The Darkness.  
Christ, Be Our Light!  
Shine In Your Church Gathered Today.</p>
<p>Longing For Food, Many Are Hungry.  
Longing For Water, Many Still Thirst.  
Make Us Your Bread, Broken For Others,  
Shared Until All Are Fed.</p>
<p>Christ, Be Our Light!  
Shine In Our Hearts.  
Shine Through The Darkness.  
Christ, Be Our Light!  
Shine In Your Church Gathered Today.</p>
<p>Longing For Shelter, Many Are Homeless.  
Longing For Warmth, Many Are Cold.  
Make Us Your Building, Sheltering Others,  
Walls Made Of Living Stone.</p>
<p>Christ, Be Our Light!  
Shine In Our Hearts.  
Shine Through The Darkness.  
Christ, Be Our Light!  
Shine In Your Church Gathered Today.</p>
<p>Many The Gifts, Many The People,  
Many The Hearts That Yearn To Belong.  
Let Us Be Servants To One Another,  
Making Your Kingdom Come.</p>
<p>In the Heavens I see your Eyes, in your eyes I see the heavens, Hallelujiah, Oh god oh what I see, will always be enough for me, why look for another moon or another Sun
-Based on Rumi</p>
<p>This being human is a guest house</p>

<p>Ins</p>
<p>Del</p>
<h3>Lords\' Prayer Araimaic</h3>
O Cosmic Birther of all Radiance and
Vibration, Soften the ground of over being
and Carve out a space within us where your
Presence can abide.
<p>Fill us with your creativity so that we May
be empowered to bear the fruit of your mission.
Let each of our actions bear fruit in accordance
with our desire,</p>
<p>Endow us with the wisdom
to produce and share what each being needs to
glow and flourish.
Untie the tangled threads of destiny that
binds us, as we release others from the
entanglement of past mistakes.</p>
<p>Do not let us be seduced by that which would divert
us from our true purpose, But illuminate the
opportunities of the present moment</p>
<p>For you are the ground and the fruitful vision, the
birth, power and fulfillment, as all is gathered
and Made whole once again,
Amen,</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>