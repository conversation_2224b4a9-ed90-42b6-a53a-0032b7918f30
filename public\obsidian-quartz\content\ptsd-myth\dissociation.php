<?php
// Auto-generated blog post
// Source: content\ptsd-myth\dissociation.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'prompts dd110 school writings hse hhs resources revisit writings  --- Author:: April Cyr Date:: 7/7/2022 Key:: Public ---  How to Handle a Dissociativ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'prompts dd110 school writings hse hhs resources revisit writings  --- Author:: April Cyr Date:: 7/7/2022 Key:: Public ---  How to Handle a Dissociativ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\ptsd-myth\\dissociation.md',
);

// Post content
$post_content = '<p>#prompts #dd110 #school #writings #hse #hhs #resources #revisit #writings</p>
<p>---
Author:: April Cyr
Date:: 7/7/2022
Key:: Public</p>
<p>---
<h2>[[How to Handle a Dissociative Episode]]</h2></p>
<p>Dissociation is a normal human response to trauma, serving as a coping mechanism for torture and disturbing memories. If someone appears spacey, unaware of their surroundings, or in a fugue state, they may be dissociating. There is no evidence suggesting that dissociated individuals are more prone to violence; in fact, they are often more likely to be victims of violence.</p>
<p>For those experiencing dissociation or supporting someone who does, a weighted blanket can be helpful. Often, individuals need space during dissociation and may require assistance to leave triggering environments. One example involves a person who dissociated in a bar displaying nazi insignia to calmly exit despite experiencing a panic attack. In this instance, dissociation was an adaptive safety mechanism.</p>
<p>Another example involves a friend undergoing a difficult legal process. She described her dissociative episodes as starting with overwhelming physical pain followed by a complete blankness.</p>
<p>Dissociation can be a warning sign of human trafficking. Recognizing these signs is crucial, as there may be limited opportunities to help someone under the control of an abuser.</p>
<p>If you notice someone dissociating, approach them gently and respectfully. Ask if they are okay or need to sit down. Offer water, food, or a quiet space. Give them time and space to calm down and gently encourage their return to the present with occasional check-ins. This can be done verbally or nonverbally, such as through facial expressions or simple questions about the surroundings.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>