<?php
/**
 * Build Script for Obsidian-Quartz Blog
 * Converts Markdown files to PHP files for the blog system
 */

// Include configuration
require_once __DIR__ . '/path-helper.php';
$config = include __DIR__ . '/config.php';
$paths = initPaths($config, __FILE__);

class MarkdownToPHPBuilder {
    private $config;
    private $paths;
    private $contentDir;
    private $templatePath;

    public function __construct($config, $paths) {
        $this->config = $config;
        $this->paths = $paths;
        $this->contentDir = $paths['base_path'] . 'content/';
        $this->templatePath = $paths['template_path'];
    }

    /**
     * Build all markdown files to PHP
     */
    public function buildAll() {
        echo "Starting build process...\n";

        $markdownFiles = $this->findMarkdownFiles($this->contentDir);
        $totalFiles = count($markdownFiles);

        echo "Found {$totalFiles} markdown files to process.\n";

        foreach ($markdownFiles as $index => $mdFile) {
            $progress = $index + 1;
            echo "[{$progress}/{$totalFiles}] Processing: {$mdFile}\n";

            try {
                $this->convertMarkdownToPhp($mdFile);
                echo "  ✅ Success\n";
            } catch (Exception $e) {
                echo "  ❌ Error: " . $e->getMessage() . "\n";
            }
        }

        echo "\nBuild complete!\n";
    }

    /**
     * Find all markdown files recursively
     */
    private function findMarkdownFiles($dir) {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'md') {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * Convert a single markdown file to PHP
     */
    private function convertMarkdownToPhp($mdFilePath) {
        $content = file_get_contents($mdFilePath);
        $relativePath = str_replace($this->contentDir, '', $mdFilePath);
        $phpFilePath = str_replace('.md', '.php', $mdFilePath);

        // Parse frontmatter and content
        $parsed = $this->parseMarkdown($content);

        // Generate PHP file content
        $phpContent = $this->generatePhpFile($parsed, $relativePath);

        // Write PHP file
        file_put_contents($phpFilePath, $phpContent);
    }

    /**
     * Parse markdown content and frontmatter
     */
    private function parseMarkdown($content) {
        $frontmatter = [];
        $markdownContent = $content;

        // Extract frontmatter if present
        if (preg_match('/^---\s*\n(.*?)\n---\s*\n(.*)$/s', $content, $matches)) {
            $frontmatterText = $matches[1];
            $markdownContent = $matches[2];

            // Parse YAML-like frontmatter
            $lines = explode("\n", $frontmatterText);
            foreach ($lines as $line) {
                if (preg_match('/^(\w+):\s*(.+)$/', trim($line), $lineMatches)) {
                    $key = $lineMatches[1];
                    $value = trim($lineMatches[2], '"\'');

                    // Handle arrays (tags)
                    if ($key === 'tags' && strpos($value, ',') !== false) {
                        $frontmatter[$key] = array_map('trim', explode(',', $value));
                    } else {
                        $frontmatter[$key] = $value;
                    }
                }
            }
        }

        // Convert markdown to HTML
        $htmlContent = $this->markdownToHtml($markdownContent);

        return [
            'frontmatter' => $frontmatter,
            'content' => $htmlContent,
            'raw_content' => $markdownContent
        ];
    }

    /**
     * Simple markdown to HTML converter
     */
    private function markdownToHtml($markdown) {
        // Basic markdown conversion
        $html = $markdown;

        // Headers
        $html = preg_replace('/^# (.+)$/m', '<h1>$1</h1>', $html);
        $html = preg_replace('/^## (.+)$/m', '<h2>$1</h2>', $html);
        $html = preg_replace('/^### (.+)$/m', '<h3>$1</h3>', $html);

        // Bold and italic
        $html = preg_replace('/\*\*(.+?)\*\*/', '<strong>$1</strong>', $html);
        $html = preg_replace('/\*(.+?)\*/', '<em>$1</em>', $html);

        // Links
        $html = preg_replace('/\[([^\]]+)\]\(([^)]+)\)/', '<a href="$2" class="external-link">$1</a>', $html);

        // Paragraphs
        $paragraphs = explode("\n\n", $html);
        $html = '';
        foreach ($paragraphs as $p) {
            $p = trim($p);
            if (!empty($p) && !preg_match('/^<[h1-6]/', $p)) {
                $html .= "<p>$p</p>\n";
            } else {
                $html .= "$p\n";
            }
        }

        return $html;
    }

    /**
     * Generate PHP file content
     */
    private function generatePhpFile($parsed, $relativePath) {
        $frontmatter = $parsed['frontmatter'];
        $content = $parsed['content'];

        // Determine path prefix based on directory depth
        $depth = substr_count($relativePath, '/');
        $pathPrefix = str_repeat('../', $depth);

        // Extract metadata
        $title = $frontmatter['title'] ?? 'Untitled';
        $author = $frontmatter['author'] ?? $this->config['site']['author'];
        $date = $frontmatter['date'] ?? date('Y-m-d');
        $excerpt = $frontmatter['excerpt'] ?? $this->generateExcerpt($parsed['raw_content']);
        $tags = $frontmatter['tags'] ?? [];

        // Generate PHP content
        $php = "<?php\n";
        $php .= "// Auto-generated blog post\n";
        $php .= "// Source: " . str_replace('/', '\\', $relativePath) . "\n\n";

        // Path helper and config loading
        $php .= "// Load path helper and configuration with fallback\n";
        $php .= "\$pathPrefix = '{$pathPrefix}';\n";
        $php .= "if (file_exists(__DIR__ . '/' . \$pathPrefix . 'path-helper.php')) {\n";
        $php .= "    require_once __DIR__ . '/' . \$pathPrefix . 'path-helper.php';\n";
        $php .= "    \$config = include __DIR__ . '/' . \$pathPrefix . 'config.php';\n";
        $php .= "} elseif (file_exists(__DIR__ . '/' . \$pathPrefix . '../path-helper.php')) {\n";
        $php .= "    require_once __DIR__ . '/' . \$pathPrefix . '../path-helper.php';\n";
        $php .= "    \$config = include __DIR__ . '/' . \$pathPrefix . '../config.php';\n";
        $php .= "} else {\n";
        $php .= "    die('Could not find path-helper.php');\n";
        $php .= "}\n";
        $php .= "\$paths = initPaths(\$config, __FILE__);\n\n";

        // Page variables
        $php .= "// Page variables\n";
        $php .= "\$page_title = " . var_export($title, true) . ";\n";
        $php .= "\$meta_description = " . var_export($excerpt, true) . ";\n";
        $php .= "\$meta_keywords = " . var_export(implode(', ', array_merge([$this->config['site']['author'], 'blog'], $tags)), true) . ";\n";
        $php .= "\$css_path = \$paths['css_path'];\n";
        $php .= "\$js_path = \$paths['js_path'];\n";
        $php .= "\$base_url = \$paths['base_path'];\n";
        $php .= "\$thumbnail = null;\n";
        $php .= "\$related_posts = [];\n";
        $php .= "\$random_posts = [];\n";
        $php .= "// This will be populated by the sidebar include\n\n";

        // Post metadata
        $php .= "// Post metadata\n";
        $php .= "\$post_data = " . var_export([
            'title' => $title,
            'author' => $author,
            'date' => $date,
            'excerpt' => $excerpt,
            'tags' => $tags,
            'source_file' => str_replace('/', '\\', $relativePath)
        ], true) . ";\n\n";

        // Post content
        $php .= "// Post content\n";
        $php .= "\$post_content = " . var_export($content, true) . ";\n\n";

        // Template inclusion
        $php .= "// Generate dynamic content\n";
        $php .= "ob_start();\n";
        $php .= "?>\n";
        $php .= "<article class=\"post-header\">\n";
        $php .= "    <?php if (isset(\$post_data['title'])): ?>\n";
        $php .= "        <h1 class=\"post-title\"><?php echo htmlspecialchars(\$post_data['title']); ?></h1>\n";
        $php .= "    <?php endif; ?>\n";
        $php .= "    \n";
        $php .= "    <?php \$metadata = []; ?>\n";
        $php .= "    <?php if (isset(\$post_data['author'])): ?>\n";
        $php .= "        <?php \$metadata[] = '<span class=\"post-author\"><i class=\"icon-user\"></i>By ' . htmlspecialchars(\$post_data['author']) . '</span>'; ?>\n";
        $php .= "    <?php endif; ?>\n";
        $php .= "    <?php if (isset(\$post_data['date'])): ?>\n";
        $php .= "        <?php \$formatted_date = (strtotime(\$post_data['date']) !== false) ? date('F j, Y', strtotime(\$post_data['date'])) : htmlspecialchars(\$post_data['date']); ?>\n";
        $php .= "        <?php \$metadata[] = '<span class=\"post-date\"><i class=\"icon-calendar\"></i>' . \$formatted_date . '</span>'; ?>\n";
        $php .= "    <?php endif; ?>\n";
        $php .= "    \n";
        $php .= "    <?php if (!empty(\$metadata)): ?>\n";
        $php .= "        <div class=\"post-meta\">\n";
        $php .= "            <?php echo implode(' | ', \$metadata); ?>\n";
        $php .= "        </div>\n";
        $php .= "    <?php endif; ?>\n";
        $php .= "    \n";
        $php .= "    <?php if (isset(\$post_data['tags'])): ?>\n";
        $php .= "        <?php \$tags = is_array(\$post_data['tags']) ? \$post_data['tags'] : [\$post_data['tags']]; ?>\n";
        $php .= "        <?php if (!empty(\$tags)): ?>\n";
        $php .= "            <div class=\"post-tags\">\n";
        $php .= "                <span class=\"tags-label\">Tags:</span>\n";
        $php .= "                <?php foreach (\$tags as \$tag): ?>\n";
        $php .= "                    <?php \$tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim(\$tag)))); ?>\n";
        $php .= "                    <a href=\"tag-<?php echo \$tag_slug; ?>.html\" class=\"tag\"><?php echo htmlspecialchars(\$tag); ?></a>\n";
        $php .= "                <?php endforeach; ?>\n";
        $php .= "            </div>\n";
        $php .= "        <?php endif; ?>\n";
        $php .= "    <?php endif; ?>\n";
        $php .= "</article>\n\n";
        $php .= "<div class=\"post-content\">\n";
        $php .= "    <?php echo \$post_content; ?>\n";
        $php .= "</div><!-- .post-content -->\n";
        $php .= "<?php\n";
        $php .= "\$content = ob_get_clean();\n\n";
        $php .= "// Include template\n";
        $php .= "include \$paths['template_path'];\n";
        $php .= "?>";

        return $php;
    }

    /**
     * Generate excerpt from content
     */
    private function generateExcerpt($content, $length = 150) {
        // Remove markdown formatting
        $text = preg_replace('/[#*\[\]()_`]/', '', $content);
        $text = preg_replace('/\n+/', ' ', $text);
        $text = trim($text);

        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . '...';
    }
}

// Command line usage
if (php_sapi_name() === 'cli') {
    $builder = new MarkdownToPHPBuilder($config, $paths);
    $builder->buildAll();
} else {
    // Web interface
    echo "<h1>Obsidian-Quartz Build Script</h1>";
    echo "<p>This script converts Markdown files to PHP files for the blog.</p>";
    echo "<p>Run from command line: <code>php build.php</code></p>";

    if (isset($_GET['build'])) {
        echo "<pre>";
        $builder = new MarkdownToPHPBuilder($config, $paths);
        $builder->buildAll();
        echo "</pre>";
    } else {
        echo "<p><a href='?build=1'>Click here to run build process</a></p>";
    }
}