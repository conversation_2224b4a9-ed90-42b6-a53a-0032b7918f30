<?php
// Auto-generated blog post
// Source: content\bs-job.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'I’m scared of losing my ‘Bullshit Job.’ a Bullshit Job is explained in this articlehttps://www.vox.com/2018/5/8/17308744/bullshit-jobs-book-davi...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'I’m scared of losing my ‘Bullshit Job.’ a Bullshit Job is explained in this articlehttps://www.vox.com/2018/5/8/17308744/bullshit-jobs-book-davi...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\bs-job.md',
);

// Post content
$post_content = '<p>I’m scared of losing my ‘Bullshit Job.’ a <a href="https://www.vox.com/2018/5/8/17308744/bullshit-jobs-book-david-graeber-occupy-wall-street-karl-marx" class="external-link">Bullshit Job is explained in this article</a> by VOX,</p>
<p>“Technology has advanced to the point where most of the difficult, labor-intensive jobs can be performed by machines. But instead of freeing ourselves from the suffocating 40-hour workweek, we’ve invented a whole universe of futile occupations that are professionally unsatisfying and spiritually empty.”</p>
<p>I’m getting less hours, and hours that are more isolating. I like the quiet time alone, but I’m faced with the situation where all the prospect certificates I am getting in technology are becoming less and less valuable because of AI.</p>
<p>Why are we allowing ourselves to become slaves of technology? Innovation was supposed to liberate us all.</p>
<p><a href="https://www.npr.org/2024/01/28/1227326215/nearly-25-000-tech-workers-laid-off-in-the-first-weeks-of-2024-whats-going-on" class="external-link">What\'s behind the tech industry\'s mass layoffs in 2024? : NPR</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>