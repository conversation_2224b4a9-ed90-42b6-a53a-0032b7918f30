<?php
// Auto-generated blog post
// Source: content\ptsd-myth\psychedelic-therapies.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Patient 7. Before: “For decades, I’ve battled depression. The awful feeling that you don’t matter, you’re not making a difference, that everyo...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Patient 7. Before: “For decades, I’ve battled depression. The awful feeling that you don’t matter, you’re not making a difference, that everyo...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\ptsd-myth\\psychedelic-therapies.md',
);

// Post content
$post_content = '<p>Patient 7. Before: “For decades, I’ve battled depression. The awful feeling that you don’t matter, you’re not making a difference, that everyone else is having a better life. The utter pointlessness of it all, getting no real enjoyment from anything.”</p>
<p>After: “There simply aren’t words to describe the experience but I can say that the usual negative self-narration that I have had vanished completely. It was replaced by a sense of beautiful chaos, a landscape of unimaginable colour and beauty. I began to see that all of my concerns about daily living weren’t relevant, that they were a result of a negative spiral. I also felt that I was learning without being taught, that intuition was being fed. Fleeting feelings from my past came back, memories too, both of which seemed long forgotten.”</p>
<p>“Although it’s early days yet, the results are amazing. I feel more confident and calm that I have in such a long time. My outlook has changed significantly too, I’m amore aware that it’s pointless to get wrapped up in endless negativity. I also feel as if I’ve seen a much clearer picture. Another side to this, is that I feel like I’ve had a second chance, like a survivor. I can enjoy things the way I used to, without the cynicism, without the oppression. At its most basic, I feel like I used to before the depression.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>