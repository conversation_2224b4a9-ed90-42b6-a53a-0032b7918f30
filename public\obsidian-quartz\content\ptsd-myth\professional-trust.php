<?php
// Auto-generated blog post
// Source: content\ptsd-myth\professional-trust.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'In the hushed corridors of professional life, where dissent often whispers rather than speaks, a silent battle unfolds for those who yearn to lend a h...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'In the hushed corridors of professional life, where dissent often whispers rather than speaks, a silent battle unfolds for those who yearn to lend a h...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\ptsd-myth\\professional-trust.md',
);

// Post content
$post_content = '<p>In the hushed corridors of professional life, where dissent often whispers rather than speaks, a silent battle unfolds for those who yearn to lend a helping hand. Imagine a labyrinth of policy, its walls towering over the individual, where the heartfelt plea can be met with a cold, unyielding silence. Transparency, a flickering lamp, often struggles to pierce the gloom, leaving spirits heavy with unspoken words.</p>
<p>This same shadow stretches into the delicate dance with clients. It\'s not merely about cushioning the fall of disagreement, but about the quiet introspection within. Picture a mirror held up to the self, revealing the subtle contours of bias. </p>
<p>As the ancient scrolls of the American Medical Association declare, </p>
<p>"Disrespectful, derogatory, or prejudiced, language or conduct or prejudiced requests for accommodation of personal preferences on the part of either patients or physicians can undermine trust and compromise the integrity of the patient-physician relationship." </p>
<p>This is a whispered truth, reminding us that respect is the sacred ground upon which all meaningful interactions are built, even when navigating the thorny paths of personal choices or challenging currents of behavior. And so, the quest begins: to weave tapestries of open dialogue, to unmask the hidden biases, and to champion ethical encounters, for these are the threads that bind us in trust and collaboration.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>