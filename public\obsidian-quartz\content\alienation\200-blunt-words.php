<?php
// Auto-generated blog post
// Source: content\alienation\200-blunt-words.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '200 Blunt Words from Minnesota Judge for Divorcing Parents';
$meta_description = 'No matter what you think of the other party—or what your family thinks of the other party—these children are one-half of each of your. Remember that, because every time you tell your child what an “idiot” his father is, or what a “fool” his mother is, or how bad the absent parent is, or what terrible things that person has done, you are telling the child half of him is bad.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '200 Blunt Words from Minnesota Judge for Divorcing Parents',
  'author' => 'Judge Michael Haas',
  'date' => '2001-01-01',
  'excerpt' => 'No matter what you think of the other party—or what your family thinks of the other party—these children are one-half of each of your. Remember that, because every time you tell your child what an “idiot” his father is, or what a “fool” his mother is, or how bad the absent parent is, or what terrible things that person has done, you are telling the child half of him is bad.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\alienation\\200-blunt-words.md',
);

// Post content
$post_content = '<p>**Minnesota Judge Has 200 Blunt Words  
for Divorcing Parents**</p>
<p>By Judge Michael Haas  
2001</p>
<p><img src="../../img/edutainment/200BluntWords.jpg" alt="200 Blunt Words Newspaper Print article." width="400"></p>
<p>“Your children have come into this world because of the two of you. Perhaps you two made lousy choices as to whom you decided to be the other parent. If so, that is your problem and your fault.</p>
<p>No matter what you think of the other party—or what your family thinks of the other party—these children are one-half of each of your. Remember that, because every time you tell your child what an “idiot” his father is, or what a “fool” his mother is, or how bad the absent parent is, or what terrible things that person has done, you are telling the child half of him is bad.</p>
<p>That is an unforgivable thing to do to a child. That is not <strong>love</strong>. That is <strong>possession</strong>. If you do that to your children, you will destroy them as surely as if you had cut them into pieces, because that is what you are doing to their emotions.</p>
<p>I sincerely hope that you do not do that to your children. Think more about your children and less about yourselves, and make yours a selfless kind of love, not foolish or selfish, or your children will suffer.”</p>
<p>Link: <a href="http://defend-yourself-go-pro-se.blogspot.com/2012/07/minnesota-judge-has-200-blunt-words-for.html" class="external-link">http://defend-yourself-go-pro-se.blogspot.com/2012/07/minnesota-judge-has-200-blunt-words-for.html</a></p>
<p><img src="../../img/edutainment/badBehaviorChildrenAdults.jpg" alt="Bad behavior in children will never be corrected by bad behavior in adults. -A Place Within Me." width="400"></p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>