<?php
// Auto-generated blog post
// Source: content\inspiration\principles-moral-revival.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Fundamental Principles of the Poor Peoples\' Campaign - A Call for Moral Revival';
$meta_description = 'We are rooted in a moral analysis based on our deepest religious and constitutional values that demand justice for all. Moral revival is necessary to save the heart and soul of our democracy.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Fundamental Principles of the Poor Peoples\' Campaign - A Call for Moral Revival',
  'author' => 'Poor People\'s Campaign - A National Call for Moral Revival',
  'date' => '2025-06-06',
  'excerpt' => 'We are rooted in a moral analysis based on our deepest religious and constitutional values that demand justice for all. Moral revival is necessary to save the heart and soul of our democracy.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\inspiration\\principles-moral-revival.md',
);

// Post content
$post_content = '<p>1. We are rooted in a moral analysis based on our deepest religious and constitutional values that demand justice for all. Moral revival is necessary to save the heart and soul of our democracy.</p>
<p>2. We are committed to lifting and deepening the leadership of those most affected by systemic racism, poverty, the war economy, and ecological devastation and to building unity across lines of division.</p>
<p>3. We will build up the power of people and state-based movements to serve as a vehicle for a powerful moral movement in the country and to transform the political, economic and moral structures of our society.</p>
<p>4. We believe in the dismantling of unjust criminalization systems that exploit poor communities and communities of color and the transformation of the "War Economy" into a "Peace Economy" that values all humanity.</p>
<p>5. We believe that equal protection under the law is non-negotiable.</p>
<p>6. We believe that people should not live in or die from poverty in the richest nation ever to exist. Blaming the poor and claiming that the United States does not have an abundance of resources to overcome poverty are false narratives used to perpetuate economic exploitation, exclusion, and deep inequality.</p>
<p>7. We will do our work in a non-partisan way-no elected officials or candidates get the stage or serve on the State Organizing Committee of the Campaign. This is not about left or right, Democrat or Republican, but about right and wrong.</p>
<p>8.  We uphold the need to do a season of sustained moral direct action as a way to break through the tweets and shift the moral narrative. We are demonstrating the power of people coming together across issues and geography and putting our bodies on the line to the issues that are affecting us all.</p>
<p>9.  We recognize that the centrality of systemic racism in maintaining economic oppression must be named, detailed and exposed empirically, morally and spiritually. Poverty and economic inequality cannot be understood apart from a society built on white supremacy.</p>
<p>10.  The Campaign and all its Participants and Endorsers embrace nonviolence. Violent tactics or
actions will not be tolerated.</p>
<p>11.  We aim to shift the distorted moral narrative often promoted by religious extremists in the nation from issues like prayer in school, abortion, and gun rights</p>
<p><img src="../../img/art/time-to-build.png" width="400" alt="Dont be fooled by 46. It\'s time to build."></p>

<p>REPAIRERS OF THE BREACH</p>
<p>KAIROS
THE CENTER FOR
RELIGIONS, RIGHTS,
AND SOCIAL JUSTICE</p>
<p>www.poorpeoplescampaign.org</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>