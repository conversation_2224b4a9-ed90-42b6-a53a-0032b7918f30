<?php
// Auto-generated blog post
// Source: content\inspiration\Sesame Street was Banned in Mississippi in 1970.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Did you know that when the beloved puppet television show, Sesame Street, aired in 1970, it was banned in the state of Mississippi for 55 days? Why wo...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Did you know that when the beloved puppet television show, Sesame Street, aired in 1970, it was banned in the state of Mississippi for 55 days? Why wo...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\inspiration\\Sesame Street was Banned in Mississippi in 1970.md',
);

// Post content
$post_content = '<p>Did you know that when the beloved puppet television show, Sesame Street, aired in 1970, it was banned in the state of Mississippi for 55 days? Why would Sesame Street be banned? This was a time of significant tension following the abolishment of racial segregation. While this was a monumental victory for social progress and the human rights of Black people, who had historically been brought to the US through chattel slavery, some individuals, authorities, and communities were resistant to the end of segregation. Sesame Street was created as a show intended to bridge the educational gap among children in the United States. It featured, and continues to feature, an integrated cast of children, which caused controversy in 1970s Mississippi. However, the ban only lasted 55 days because Sesame Street is undeniably awesome, and it was very difficult to convince the public otherwise. Having been on air for fifty-five years, there have been 54 seasons and allegedly over 4,500 episodes as of its 50th anniversary in 2019. These episodes have been translated and dubbed into over 160 versions in 70 different languages, reaching over 190 million children globally. One particularly delightful Sesame Street appearance, which can be found on YouTube, is the Tiny Desk Concert hosted by National Public Radio (NPR), where the puppet cast performs a 20-minute concert in the NPR office – simply fantastic.</p>
<p>Now, considering television as a whole technology, it presents a somewhat problematic picture. Many technological advancements come with considerable drawbacks in our lives. Families often experience less engagement when the television is on. Harmful and inaccurate information spreads rapidly through television, potentially hindering our critical thinking skills. Additionally, many individuals find themselves addicted to their screens. Yet, without television, we wouldn\'t have Sesame Street.</p>
<p>Even if we subscribe to the idea that a technology is largely detrimental to humanity and the planet, we can still derive something positive from it.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>