<?php
// Auto-generated blog post
// Source: content\kitchen\Wishlist for High-Quality Ingredients.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Wishlist for High-Quality Ingredients Pantry Staples: - Cooking Oils: Extra virgin olive oil, avocado oil, toasted sesame oil - Vinegars: Balsamic v...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Wishlist for High-Quality Ingredients Pantry Staples: - Cooking Oils: Extra virgin olive oil, avocado oil, toasted sesame oil - Vinegars: Balsamic v...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kitchen\\Wishlist for High-Quality Ingredients.md',
);

// Post content
$post_content = '<h2>Wishlist for High-Quality Ingredients</h2>
<p><strong>Pantry Staples:</strong></p>
<p>- <strong>Cooking Oils:</strong> Extra virgin olive oil, avocado oil, toasted sesame oil
- <strong>Vinegars:</strong> Balsamic vinegar, apple cider vinegar, red wine vinegar
- <strong>Non-dairy Milks:</strong> Oat milk, almond milk, coconut milk
- <strong>Beans:</strong> Black beans, pinto beans, kidney beans, chickpeas (garbanzo beans)
- <strong>Rice:</strong> Basmati rice, jasmine rice, brown rice
- <strong>Pasta:</strong> Whole wheat pasta, brown rice pasta, lentil pasta
- <strong>Grains:</strong> Quinoa, barley, farro
- <strong>Nuts & Seeds:</strong> Almonds, walnuts, cashews, flax seeds, chia seeds
- <strong>Dried Fruits:</strong> Cranberries, raisins, cherries, chopped dates
- <strong>Spices:</strong> Smoked paprika, cumin, cayenne pepper, turmeric, curry powder, dried herbs (thyme, oregano, rosemary)
- <strong>Sweeteners:</strong> Maple syrup, honey, agave nectar</p>
<p><strong>Fresh Produce:</strong></p>
<p>- <strong>Seasonal Fruits:</strong> Apples, pears, citrus fruits (oranges, grapefruits, clementines), berries (strawberries, blueberries, raspberries)
- <strong>Seasonal Greens:</strong> Mixed greens, spinach, kale, arugula, swiss chard, herbs (fresh basil, parsley, cilantro, mint)
- <strong>Vegetables:</strong> Bell peppers, onions, carrots, broccoli, cauliflower, sweet potatoes, mushrooms, tomatoes (in season)
- <strong>Aromatics:</strong> Garlic, ginger, shallots</p>
<p><strong>Other:</strong></p>
<p>- <strong>Beverages:</strong> Coffee beans, loose leaf tea, sparkling water, kombucha (optional)
- <strong>Baking essentials:</strong> Flour (all-purpose, whole wheat, gluten-free if needed), sugar, baking powder, baking soda, cocoa powder
- <strong>Plating Garnishes:</strong> Edible flowers (violets, pansies, nasturtiums), microgreens, citrus zest, toasted nuts & seeds</p>
<p><strong>Optional High-End Additions:</strong></p>
<p>- Specialty cooking oils: Truffle oil, walnut oil
- Aged balsamic vinegar
- Specialty cheeses (goat cheese, blue cheese)
- Locally sourced honey or maple syrup
- Fresh herbs (basil, thyme, rosemary) in pots
- Local, artisanal breads and crackers</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>