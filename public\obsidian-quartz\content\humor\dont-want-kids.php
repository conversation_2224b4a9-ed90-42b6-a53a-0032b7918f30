<?php
// Auto-generated blog post
// Source: content\humor\dont-want-kids.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'List of things to say when someone asks why you don\'t want kids';
$meta_description = 'Reposted from nerdfighterwhatevernumbers on Pinterest.com';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'List of things to say when someone asks why you don\'t want kids',
  'author' => 'nerdfighterwhatevernumbers on Pinterest',
  'date' => '2025-07-27',
  'excerpt' => 'Reposted from nerdfighterwhatevernumbers on Pinterest.com',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\humor\\dont-want-kids.md',
);

// Post content
$post_content = '<p>Reposted from nerdfighterwhatevernumbers on Pinterest.com</p>
<p>+ I promised my firstborn to a witch and really don\'t want to make good on the deal
+  Well you can have them FOR me if it\'s that big a deal to you
+ I don\'t think I could get a good price for em on the black market
+  Fight me Helen
+ I can\'t be a better parent than Angelina Jolie so why even bother
+ That\'s my nindo. My ninja way.
+ I literally JUST sat down
+ Recite "The Highway Man" from Over the Garden Wall
+ Kids? What are those? I don\'t understand. What are these you OH GRAVY WHAT IS THAT !?
+ Oohhh no, I\'ve seen Disney movies, I know what happens to mothers
+ Centipedes? In my vagina?
+ <em>Angrily</em> YOU SEE !? This is just like that episode of Spongebob! + <em>insert the plot of any episode of Spongebob in excruciating detail</em>
+ I heard they\'re .. you know .. itchy. Like, as soon as you have a kid  Just totally itchy. Everything.
+ I\'m an Aries
+ Well, we already got an even number so .. <em>shrug</em>
+ I must first capture the Avatar to regain my honor
+ I\'m allergic
 That\'s just what the communists want!
+ I\'ve been dead for seven years
+ Santa didn\'t bring me one last Christmas, so I guess it\'s no meant to e
+ I\'m afraid they\'ll have bad taste in memes
+ It would be unfair to my cat
+ I\'m chaotic neutral
+ <em>long farting noise lasting at least 45 seconds</em>
+ "I don\'t want to have children, I want to stay single, and let my hair flow
in the wind as I ride through the glen firing arrows into the sunset."</p>
<p><img src="../../img/humor/why-i-dont-want-kids.png" alt="why-i-dont-want-kids.png" width="400"></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>