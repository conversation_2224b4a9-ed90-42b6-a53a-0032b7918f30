<?php
// Auto-generated blog post
// Source: content\index.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Welcome to my Digital Garden';
$meta_description = 'Hello Li Huahttp://upworthy.com/rednote-letters-from-li-hua, You can call me <PERSON><PERSON> <PERSON><PERSON> or just A. A. if you’re in a hurry. Here’s the deal: I m...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Welcome to my Digital Garden',
  'author' => 'A. A. Chips',
  'date' => '2025-05-11',
  'excerpt' => 'Hello Li Huahttp://upworthy.com/rednote-letters-from-li-hua, You can call me A. A. Chips or just A. A. if you’re in a hurry. Here’s the deal: I m...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\index.md',
);

// Post content
$post_content = '<p>Hello <a href="http://upworthy.com/rednote-letters-from-li-hua" class="external-link">Li Hua</a>,</p>
<p>You can call me A. A. Chips (or just A. A. if you’re in a hurry).</p>
<p>Here’s the deal: I might become your favorite parasocial weirdo—the logical family member you never knew you needed. Or, depending on who’s asking, I’m the villain of someone else’s story. I’m at peace with either. You get to decide.</p>
<p>I owe you an apology. When your letters arrived in grade school, I mistook them for homework (and let’s be real—most of us don’t do our homework). Later, as an adult, I assumed they were tax threats or spam. (Who actually checks their mail? It’s a minefield of dread.) But here we are. Better late than never, right? <a href="http://upworthy.com/rednote-letters-from-li-hua">What am I talking about?</a></p>
<p>This website is my way of handing you a USB drive stuffed with my brain. Corporations want my data for their gross little algorithms. They only care about one thing, and it\'s disgusting.. but you — you get the raw, unfiltered archive. No middlemen.</p>
<p>A friend once told me:</p>
<p><blockquote>"Sometimes you have to make peace with being the villain in someone else’s story. You don’t get to dictate their narrative—even if you’d rewrite it differently."</blockquote></p>
<p>Turns out, that’s freeing. Some people need to paint you as the bad guy so they don’t have to face their own shit. That’s their business.</p>
<p>Navigate this maze however you like:</p>
<p><a href="your-page.php" class="adventure-link">Your Text</a>
<a href="your-page.php" class="story-path">Your Text</a>
<a href="your-page.php" class="mystical-link">Your Text</a></p>
<p>For Quest Cards:
<a href="your-page.php" class="quest-card">
  <div class="quest-title">Your Title</div>
  <div class="quest-description">Your description</div>
</a></p>
<p><strong>Money talk (awkward, but necessary):</strong>  
If anything here ruins your day in a _delightful_ way, consider tossing a coin to your witcher via the <strong>\'Support\'</strong> button. It fuels my work and lets me pay it forward. (<a href="https://www.ko-fi.com/aachips" class="external-link">Ko-Fi link</a> for the curious.)</p>
<p><strong>⚠️ Caution:</strong>  
Believe whatever you want about me. I won’t chase you. But physics? Physics doesn’t forgive bad data. Tread lightly.</p>
<p><img src="../img/were-all-the-villain.png" alt="We’re all the villain in someone’s story" width="250"></p>
<p><figure class="polaroid-frame">
<!-- vintage-frame, minimal-frame, sketch-frame -->
  <img src="your-image.jpg" alt="Description" data-caption="Modal caption">
  <figcaption>Your caption text</figcaption>
</figure></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>