<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\<PERSON> - Turn the World Around (Official Audio).md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = '<PERSON> - Turn the World Around Official Audio <iframe width="560" height="315" src="https://www.youtube.com/embed/PeZubMTLaTU?si=tnfTDkGzphVU...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Harry Belafonte - Turn the World Around Official Audio <iframe width="560" height="315" src="https://www.youtube.com/embed/PeZubMTLaTU?si=tnfTDkGzphVU...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Harry Belafonte - Turn the World Around (Official Audio).md',
);

// Post content
$post_content = '<h1>Harry Belafonte - Turn the World Around (Official Audio)</h1>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/PeZubMTLaTU?si=tnfTDkGzphVURBH4" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<p>https://www.youtube.com/watch?v=PeZubMTLaTU</p>
<p>129,862 views Feb 8, 2017 <a href="https://www.youtube.com/hashtag/belafonte" class="external-link">#Belafonte</a> <a href="https://www.youtube.com/hashtag/harrybelafonte" class="external-link">#HarryBelafonte</a> <a href="https://www.youtube.com/hashtag/officialaudio" class="external-link">#OfficialAudio</a></p>
<p>“Turn The World Around” by Harry Belafonte Listen to Harry Belafonte: <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbWtYNmNJUlFrWTNZbHJVN3hvRmI3LWdTN1FjUXxBQ3Jtc0tsaVFnQ184V2laU2RpdndDaldEWVdIUWRKM1FmSmJ5NjRoX0xycnU0UXhFN2xJRi01UWlRU2VPRFZpdHRlczBlaG50TnlVbHAyMDFBMXlRdXFhc0tWTS0yM0c5QUU5SkwzS09PX1UtdEZkVHRzUzVKTQ&q=https%3A%2F%2FHarryBelafonte.lnk.to%2FlistenYD&v=PeZubMTLaTU" class="external-link">https://HarryBelafonte.lnk.to/listenYD</a> Subscribe to Harry Belafonte on YouTube: <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbGdYX0JsUV9RZXpjRnp0cy1kSXVXUC1heXprZ3xBQ3Jtc0ttV1hROTdDV2JCOGx6aVo0QTF6OWtmRm5rV2RnblhaU2YtQ2ZQY3lzRWtoZXAwWXl5T3NFOG5Rc0djbm5fOGNRSU9FQ3pnU3d5VmhPWnB6d3dIZ0FXX1E4Sng3ak1MWmVDQ3J5b1VkNjZZMmlVNi1GRQ&q=https%3A%2F%2FHarryBelafonte.lnk.to%2FsubscribeYD&v=PeZubMTLaTU" class="external-link">https://HarryBelafonte.lnk.to/subscri...</a> Follow Harry Belafonte: Facebook: <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbF9zWE8zQm9aZjFuVUxlc3MyWFMwMzdYY21td3xBQ3Jtc0trdjVZWExYMjF6ZzRfSmtrX3FXeEpaUGJ3MUlic0cycWNLdjhnLXhfLTVKVWJPSGt3b3BSOUwzWkpGVW9SZ0d2OU9wV0dMMkZIYTBFWDZCNnNBbm1rOEpXSkhBc2J0WjlBZUFPckh2TnFWZVk4N0dEZw&q=https%3A%2F%2FHarryBelafonte.lnk.to%2FfollowFI&v=PeZubMTLaTU" class="external-link">https://HarryBelafonte.lnk.to/followFI</a> Twitter: <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbkN4X2xBaFkwbjVHalpEb2QyaW5FaXp6b1E1UXxBQ3Jtc0trdUZCRktJc1VCNmtlOXpTaFUtdHVCVm1iSFY0ZFNKNG1kMXhKcEpWbDNRZHZZTXRaVVNMTlpEYkRqZThSX3NtdkNpcmFNMWR5R2NKZ3EwZkwwaXFxMWtaZjFIclpLQ2JmVVliQUNLcW9VOVFlcWhrWQ&q=https%3A%2F%2FHarryBelafonte.lnk.to%2FfollowTI&v=PeZubMTLaTU" class="external-link">https://HarryBelafonte.lnk.to/followTI</a> Spotify: <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbUZtNnV6bUxibXc0T1hTVllKNGVPV3NCVEdRd3xBQ3Jtc0traHRKcG9GZGR0WWRPZ2dmOUdlRnJtWmJBZ3FPMGVmd1p0eDJYdmx1T1kxUnFzWDd1NDduX2VzY2RVUkZOT1VGRTFReGh4MG5PYlNWcVJCVEFBLXJBOUZGZktyRkZvdnZxNVB0LXQ1Z1pkalFKMVlrYw&q=https%3A%2F%2FHarryBelafonte.lnk.to%2FfollowSI&v=PeZubMTLaTU" class="external-link">https://HarryBelafonte.lnk.to/followSI</a></p>
<p>Most notably featured in a <a href="http://muppet.wikia.com/wiki/Episode_314:_Harry_Belafonte" class="external-link">1979 episode of “The Muppet Show”</a>, Belafonte’s “Turn the World Around,” is a passionately triumphant anthem about finding your heritage and identity, especially in a society that represses diversity and ethnicity.</p>
<p>![](https://images.genius.com/1560541b452cba81ac891f7df90b04bf.480x320x40.gif)</p>
<p>On “The Muppet Show,” Belafonte performed alongside specially-made puppets designed to look like African masks. Belafonte himself had a hand in choosing the designs, and took great care to ensure they weren’t offensive due to religious or cultural significance.</p>
<p>[Verse 1]  
We come from the fire  
Living in the fire  
Go back to the fire  
Turn the world around  
We come from the fire  
Living in the fire  
Go back to the fire  
Turn the world around  
We come from the fire  
Living in the fire  
Go back to the fire  
Turn the world around  
  
[Verse 2]  
We come from the water  
Living in the water  
Go back to the water  
Turn the world around  
We come from the water  
Living in the water  
Go back to the water  
Turn the world around  
We come from the water  
Living in the water  
Go back to the water  
Turn the world around</p>
<p>[Verse 3]  
We come from the mountain  
Living on the mountain  
Go back to the mountain  
Turn the world around  
We come from the mountain  
Living on the mountain  
Go back to the mountain  
Turn the world around  
We come from the mountain  
Living on the mountain  
Go back to the mountain  
Turn the world around  
  
[Refrain]  
Oh, oh, so is life  
Ah, ha, so is life  
Oh, oh, so is life  
Ah, ha, so is life  
  
[Verse 4]  
Do you know who I am?  
Do I know who you are?  
See we one another clearly  
Do we know who we are?  
Do you know who I am?  
Do I know who you are?  
See we one another clearly  
Do we know who we are?  
Do you know who I am?  
Do I know who you are?  
See we one another clearly  
Do we know who we are?</p>
<p>Water make the river  
River wash the mountain  
Fire make the sunlight  
Turn the world around  
  
Heart is of the river  
Body is the mountain  
Spirit is the sunlight  
Turn the world around  
  
We are of the spirit  
Truly of the spirit  
Only can the spirit  
Turn the world around  
  
We are of the spirit  
Truly of the spirit  
Only can the spirit  
Turn the world around</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>