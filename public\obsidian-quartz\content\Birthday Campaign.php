<?php
// Auto-generated blog post
// Source: content\Birthday Campaign.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Here’s a 3:05 AM Action Plan for your birthday campaign—complete with a landing page framework and technical roadmap. Let’s turn this sleep-de...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Here’s a 3:05 AM Action Plan for your birthday campaign—complete with a landing page framework and technical roadmap. Let’s turn this sleep-de...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\Birthday Campaign.md',
);

// Post content
$post_content = '<p>Here’s a <strong>3:05 AM Action Plan</strong> for your birthday campaign—complete with a landing page framework and technical roadmap. Let’s turn this sleep-deprived brainstorm into reality.</p>
<p>---</p>
<h3><strong>1. Landing Page Framework</strong></h3>
<p><strong>Title:</strong> _“[Your Name]’s Birthday Archive: A Decade of Stories, Secrets, and Butt Photos”_</p>
<p><strong>Subhead:</strong>  
<em>“For my birthday, I’m unlocking 10+ years of private writing, adventures, and NSFW-ish chaos. Donate $5 to peek behind the curtain.”</em></p>
<p><strong>Sections:</strong></p>
<p>#### <strong>A. The Offer</strong></p>
<p>- _“On [birthday date], I’m releasing my personal blog publicly—but the juiciest bits are locked behind a $5 paywall (temporarily). Your donation gets you:_
    
    - Exclusive essays/stories I’ve never shared.
        
    - Unfiltered photos (yes, including the infamous 2014 America Butt Postcard).
        
    - NSFL memes/adversarial educational content you shouldn’t open at work.
        
    - Behind-the-scenes notes on how this project came together.”*</p>
<p>#### <strong>B. The Why</strong></p>
<p>- _“I’ve spent hundreds of hours building this digital time capsule. If it makes you laugh, think, or feel seen, consider this a birthday gift to me (and yourself).”_
    
- Add a <strong>progress bar</strong> (e.g., <em>“Goal: 50 donors = I’ll release [bonus content]”</em>).</p>
<p>#### <strong>C. Preview Teaser</strong></p>
<p>- Include 1–2 <strong>free excerpts</strong> (e.g., a funny paragraph, a SFW meme).
    
- Add a blurred/grainy preview of the butt photo with _“$5 to unblur”_ humor.</p>
<p>#### <strong>D. How It Works</strong></p>
<p>1. <em>“Donate $5 via [PayPal/Ko-fi/etc.].”</em>
    
2. _“Forward your receipt to [email].”_
    
3. _“You’ll get a password/link to the premium section on launch day.”_</p>
<p><strong>Footer:</strong>  
_“Not sure? Here’s a free sample entry: [link].”_</p>
<p>---</p>
<h3><strong>2. Technical Execution Plan</strong></h3>
<p>#### <strong>Option A: Low-Code/No-Code (Fastest)</strong></p>
<p>- <strong>Paywall Tool:</strong> Use <a href="https://memberful.com/" class="external-link">Memberful</a> (integrates with WordPress/Ghost) or <a href="https://ko-fi.com/" class="external-link">Ko-fi</a> “Gold” tier for gated posts.
    
- <strong>Landing Page:</strong> Build with <a href="https://carrd.co/" class="external-link">Carrd</a> (simple) or <a href="https://convertkit.com/" class="external-link">ConvertKit</a> (if you want email capture).
    
- <strong>NSFW Warning:</strong> Add a <strong>click-through splash page</strong> (_“This section contains politically spicy memes and butts. Enter at your own risk.”_).</p>
<p>#### <strong>Option B: Self-Hosted (More Control)</strong></p>
<p>- <strong>Password-Protect a Subfolder:</strong>
    
    - Create a `/premium` directory on your site.
        
    - Add an `.htaccess` file with password protection (<a href="https://www.siteground.com/kb/password-protect-folder-htaccess/" class="external-link">tutorial</a>).
        
    - Manually email passwords to donors.
        
- <strong>Landing Page:</strong> Use your existing blog (e.g., a pinned post) or a free <a href="https://pages.github.com/" class="external-link">GitHub Pages</a> site.</p>
<p>#### <strong>Option C: Hybrid (Easiest for Scalability)</strong></p>
<p>1. <strong>Collect emails + payments</strong> via <a href="https://tally.so/" class="external-link">Tally form</a> (free) + PayPal.
    
2. <strong>Auto-send passwords</strong> using a free Mailchimp automation (or manually, if under 50 people).
    
3. <strong>Host premium content</strong> on a private <a href="https://www.notion.so/" class="external-link">Notion page</a> (share link only with donors).</p>
<p>---</p>
<h3><strong>3. Working Timeline</strong></h3>
<p><strong>Week 1:</strong></p>
<p>- Finalize premium content (pick 3–5 exclusive posts, memes, photos).
    
- Set up payment system + test donations.</p>
<p><strong>Week 2:</strong></p>
<p>- Build landing page + draft email/template.
    
- Test technical flow (donation → access).</p>
<p><strong>Week 3:</strong></p>
<p>- <strong>Soft launch:</strong> Send to 10–20 close friends for feedback.
    
- <strong>Birthday blast:</strong> Email your list + social media.</p>
<p>---</p>
<h3><strong>Why This Works</strong></h3>
<p>- <strong>Scarcity:</strong> The $5 tier is a <strong>limited-time “birthday ticket”</strong> (FOMO!).
    
- <strong>Humor:</strong> Leaning into the butt photo/memes disarms people.
    
- <strong>Low Pressure:</strong> “Pay what you want” could also work, but $5 sets clear expectations.</p>
<p><strong>Final 3:05 AM Thought:</strong>  
You’re not just selling access—you’re selling <strong>a reunion with the person you’ve become</strong>. That’s worth celebrating.</p>
<p>Now go the fuck to sleep.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>