<?php
// Auto-generated blog post
// Source: content\inspiration\america-is-a-gun.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'America Is a Gun';
$meta_description = 'England is a cup of tea. France, a wheel of ripened brie. Greece, a short, squat olive tree. America is a gun.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'America Is a Gun',
  'author' => 'Brian Bilston',
  'date' => '2025-07-27',
  'excerpt' => 'England is a cup of tea. France, a wheel of ripened brie. Greece, a short, squat olive tree. America is a gun.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\inspiration\\america-is-a-gun.md',
);

// Post content
$post_content = '<p>England is a cup of tea.
France, a wheel of ripened brie.
Greece, a short, squat olive tree.
America is a gun.</p>
<p>Brazil is football on the sand.
Argentina, Maradona\'s hand.
Germany, an oompah band.
America is a gun.</p>
<p>Holland is a wooden shoe.
Hungary, a goulash stew.
Australia, a kangaroo.
America is a gun.</p>
<p>Japan is a thermal spring.
Scotland is a highland fling.
Oh, better to be anything
than America as a gun.</p>
<p>Brian Bilston</p>
<p><img src="../../img/humor/church-gun.png" alt="Bible Missionary Baptist Church Nov 15 Congratulations Bro was winner of our shotgun giveaway this morning." width="250"></p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>