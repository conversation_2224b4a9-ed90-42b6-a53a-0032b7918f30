<?php
// Auto-generated blog post
// Source: content\playlists\assets\mercy\Sword to Ploughshare English Spanish.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Que toda puebla se unan en armonía,   Que toda nación busquen la paz.   Que no haya más guerra, ni odio ni violencia. Que sus armas se conviertan e...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Que toda puebla se unan en armonía,   Que toda nación busquen la paz.   Que no haya más guerra, ni odio ni violencia. Que sus armas se conviertan e...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\mercy\\Sword to Ploughshare English Spanish.md',
);

// Post content
$post_content = '<p>Que toda puebla se unan en armonía,  
Que toda nación busquen la paz.  
Que no haya más guerra, ni odio ni violencia.</p>
<p>Que sus armas se conviertan en arados,  
y que las naciones nunca más conozcan la guerra.</p>
<p>Que así sea, que así sea, que así sea.</p>
<p>Que sus armas se conviertan en arados,  
y que la violencia se desvanezca en el aire.</p>
<p>Y bajo la higuera y la vid, vivirán en paz,  
sin temor y con esperanza en su corazón.</p>
<p>Que así sea, que así sea, que así sea.</p>
<p>May all people unite in harmony,
May every nation seek peace.
Let there be no more war, no hatred or violence.</p>
<p>Let your weapons become plows,
and may nations never again know war.</p>
<p>So be it, so be it, so be it.</p>
<p>Let your weapons become plows,
and that the violence vanishes into the air.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>