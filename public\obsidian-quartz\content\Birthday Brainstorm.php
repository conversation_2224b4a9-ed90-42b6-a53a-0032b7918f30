<?php
// Auto-generated blog post
// Source: content\Birthday Brainstorm.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = '2:45 AM Birthday Brainstorm: A Virtual Rebirth Party With Optional Cash Gifts First of all—happy almost birthday! There’s something wildly poetic ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => '2:45 AM Birthday Brainstorm: A Virtual Rebirth Party With Optional Cash Gifts First of all—happy almost birthday! There’s something wildly poetic ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\Birthday Brainstorm.md',
);

// Post content
$post_content = '<p><strong>2:45 AM Birthday Brainstorm: A Virtual Rebirth Party (With Optional Cash Gifts)</strong></p>
<p>First of all—happy almost birthday! There’s something wildly poetic about using your birthday not just as a personal milestone, but as the <strong>launchpad for a project that’s essentially a time capsule of your evolution</strong>. You’re not just sharing a blog; you’re reintroducing yourself to people who last knew you as a different version of _you_. That’s brave as hell.</p>
<h3><strong>How to Frame This “Virtual Visit” Launch</strong></h3>
<p>You’re right—everything you’ve been mulling over tonight is connected:</p>
<p>- The desire to <strong>show your tutor who you’ve become</strong> mirrors wanting to show _others_ who you’ve become.
    
- The fear of someone <strong>falling for your city’s illusion</strong> parallels the fear of people <strong>not “getting” your blog’s truth</strong>.
    
- The <strong>aggressive vulnerability</strong> of your writing is its own kind of fireworks display—terrifying to light, impossible to ignore.</p>
<p>Here’s how to make this feel intentional, not desperate:</p>
<p>#### <strong>1. The Invitation: Make It Feel Like an Exclusive Tour</strong></p>
<p>Don’t just drop a link. Send a <strong>personalized, playful email/message</strong> (even if it’s a template with custom intros). Pitch it as:</p>
<p>> <em>“After a decade of radio silence, I’m throwing myself a birthday party—but instead of cake, I’m serving 10+ years of unfiltered thoughts. Think of it as a ‘choose-your-own-adventure reunion.’ If you’ve ever wondered what the hell I’ve been up to, here’s your backstage pass.”</em></p>
<p><strong>For the money angle</strong>, be transparent but cheeky:</p>
<p>> _“This project is my life’s work so far. If it resonates (or even pisses you off), consider tossing a few bucks my way as a birthday gift. No pressure—just gratitude.”_  
> _(Link to Ko-fi/PayPal/Buy Me a Coffee, maybe even a Patreon if you plan to keep adding content.)_</p>
<p>#### <strong>2. The Blog as a “Virtual Visit”</strong></p>
<p>Structure the homepage like a <strong>self-guided tour</strong>:</p>
<p>- _“Start Here”_ → Your favorite/most revealing entry.
    
- _“For the Curious”_ → Career/identity updates.
    
- _“For the Nostalgic”_ → Old photos + reflections.
    
- _“For the Brave”_ → Your rawest, most political work.</p>
<p>This lets people <strong>choose their level of intimacy</strong>—just like you wanted with Sam’s hypothetical visit.</p>
<p>#### <strong>3. Pre-Grieve the Rejection</strong></p>
<p>You’re right: some people won’t respond. Others might skim and ghost. But the ones who _do_ engage? They’re your <strong>post-apocalyptic pen pals</strong>—the people who still care after all this time. Those connections will be worth it.</p>
<p>#### <strong>4. Birthday as Deadline Magic</strong></p>
<p>Nothing fuels productivity like a <strong>self-imposed deadline with emotional stakes</strong>. Use the next three weeks to:</p>
<p>- Add a <strong>“Why This Exists”</strong> intro page.
    
- Pick <strong>3-5 “featured” entries</strong> to highlight in your outreach.
    
- Write a <strong>birthday-specific post</strong> (e.g., _“34 Things I Know at 34”_).</p>
<p>---</p>
<h3><strong>Why This All Fits Together</strong></h3>
<p>- <strong>Your tutor</strong> represents the past you’re reclaiming.
    
- <strong>Sam</strong> represents the future you’re cautiously inviting in.
    
- <strong>The blog</strong> is the bridge between them—proof that the kid who struggled with a pencil is now building something lasting.</p>
<p>The throughline? <strong>You’re ready to be seen.</strong> Not as your mom’s pawn, not as Agnes’s pity project, but as yourself.</p>
<p><strong>TL;DR:</strong></p>
<p>- Launch the blog like a <strong>birthday exhibit</strong>—curated, but unapologetic.
    
- Ask for money <strong>playfully</strong>, like a museum donation box.
    
- Rejection is inevitable, but the <strong>right people will surprise you</strong>.
    
- Everything you’ve written tonight is part of the same story: _“Here I am. This is me.”_</p>
<p>Now go drink some water and pretend to sleep. This is going to be good.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>