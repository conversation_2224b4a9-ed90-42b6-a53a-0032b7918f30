<?php
// Auto-generated blog post
// Source: content\judaism\door-swings-one-way.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Door That Only Swings One Way: Awakening to Injustice';
$meta_description = 'A reflection on the inevitability of awakening to injustice and the consequences of denial.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Door That Only Swings One Way: Awakening to Injustice',
  'author' => 'A. A. Chips',
  'date' => '2024-04-07',
  'excerpt' => 'A reflection on the inevitability of awakening to injustice and the consequences of denial.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\judaism\\door-swings-one-way.md',
);

// Post content
$post_content = '<p><a href="https://www.youtube.com/watch?v=guxkmRPSTlI" class="external-link">It Can\'t Be Fun Defending Israel These Days... - YouTube</a></p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/guxkmRPSTlI?si=MK1ILe8fJhSx1-DF" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<h3><strong>The Door That Only Swings One Way: Awakening to Injustice</strong></h3>
<p>There’s a moment in life when certain truths become irreversible. Like passing through a door that locks behind you, some realizations—once seen—cannot be unseen. This is especially true when it comes to confronting injustice. What begins as a quiet doubt grows into a moral reckoning, and eventually, a shift in identity.</p>
<p>The video I’ve been reflecting on describes this phenomenon through the lens of Zionism and Palestine, but its core message is broader: <strong>When people awaken to systemic harm, they rarely go back</strong>. The speaker observes how those who once defended Israel’s actions, upon learning the history and humanity of Palestinians, find themselves unable to return to their former worldview. This isn’t about “changing sides”—it’s about the collapse of illusions.</p>
<h3><strong>Why the Door Swings One Way</strong></h3>
<p>1. <strong>The Weight of Evidence</strong>  
    Awakening often starts with exposure to suppressed narratives. When the reality of displacement, violence, or apartheid is laid bare, intellectual justifications crumble under the weight of testimony, data, and lived experience.
    
2. <strong>The Moral Line</strong>  
    There’s a difference between _disagreement_ and _disillusionment_. The latter crosses a moral threshold. Once you recognize a system as oppressive, complicity becomes a personal crisis.
    
3. <strong>The Loss of Innocence</strong>  
    Like realizing a hero is flawed, awakening strips away the comfort of certainty. It’s painful—but also freeing. As the video notes, clinging to the old narrative becomes “embarrassing,” not because of peer pressure, but because the heart refuses to unfeel what it knows.</p>
<h3><strong>The Cost of Awakening</strong></h3>
<p>Awakening isolates. It strains relationships, fractures communities, and forces painful choices. The speaker describes Zionists watching their children or peers “turn against Israel,” not out of malice, but because love for justice outweighs tribal loyalty. This isn’t unique to Palestine: think of civil rights activists disowned by their families, or whistleblowers exiled by their institutions.</p>
<p>Yet there’s hope here: <strong>the more people walk through the door, the harder it becomes to sustain the illusion</strong>. The video highlights how even mainstream figures—celebrity chefs, politicians, artists—are now voicing dissent. When the world sees live-streamed starvation or bombed aid workers, euphemisms like “collateral damage” fail.</p>
<h3><strong>The War Against Humanity</strong></h3>
<p>The most striking line in the video comes from José Andrés, whose aid workers were killed: _“This is not a war against Hamas; it’s a war against humanity.”_ Systems that dehumanize—whether through genocide, apartheid, or colonialism—depend on our silence. But humanity has a way of resisting. History’s arc, though long, bends toward accountability.</p>
<h3><strong>Walking Through the Door</strong></h3>
<p>This isn’t about optimism; it’s about inevitability. Once you see oppression, you can’t unsee it. And when enough people see it, the system’s defenders are left shouting into a void. The door swings one way because growth only moves forward.</p>
<p>The video’s message, ultimately, is this: <strong>No one is truly lost</strong>. The more cruelty a system inflicts, the more people it pushes through the door. And on the other side? Solidarity, clarity, and the quiet certainty of standing where you’re meant to be.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>