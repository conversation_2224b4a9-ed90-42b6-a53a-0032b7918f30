<?php
// Auto-generated blog post
// Source: content\journal\no-fly-zone.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'No Fly Zone - January 9, 2017';
$meta_description = 'Guilt and shame are major struggles right now. I want no contact with my mother. I\'m also relapsing into a video game addiction from a decade ago, which she triggered. I\'ve been avoiding her and am close to blocking her. Her threat to report me as a missing person doesn\'t feel like genuine concern but rather an attempt to control me, as it always has been.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'No Fly Zone - January 9, 2017',
  'author' => 'A. A. Chips',
  'date' => '2017-01-09',
  'excerpt' => 'Guilt and shame are major struggles right now. I want no contact with my mother. I\'m also relapsing into a video game addiction from a decade ago, which she triggered. I\'ve been avoiding her and am close to blocking her. Her threat to report me as a missing person doesn\'t feel like genuine concern but rather an attempt to control me, as it always has been.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\no-fly-zone.md',
);

// Post content
$post_content = '<p>I chose not to attend the Masters in Social Work program in Baltimore, and I stand by that decision. Starting this March would have been the time, but it\'s not the right path for me. I have clarity on my learning objectives and career aspirations, and I\'m very close to achieving my goals. However, I\'ve hit a significant obstacle just before the finish line, coinciding with instability in my personal life. I\'m facing potential homelessness due to my own emotional barriers. While Sue has offered her home, I feel like a burden. My stay with Sophia ended due to visitor limitations. Susan has offered her basement, but I feel uneasy about it. Guilt and shame are major struggles right now. I want no contact with my mother. I\'m also relapsing into a video game addiction from a decade ago, which she triggered. I\'ve been avoiding her and am close to blocking her. Her threat to report me as a missing person doesn\'t feel like genuine concern but rather an attempt to control me, as it always has been. I see her as a helicopter parent, and at 24, that behavior is tiresome and feels like harassment. I\'m setting firm boundaries with her.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>