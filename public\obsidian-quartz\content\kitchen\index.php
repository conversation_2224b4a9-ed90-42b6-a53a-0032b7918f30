<?php
// Auto-generated blog post
// Source: content\kitchen\index.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Apple Chip Kitchen';
$meta_description = 'Welcome to the Apple Chip Kitchen - where food meets philosophy, recipes meet sustainability, and every meal is an opportunity for mindful living.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Apple Chip Kitchen',
  'author' => 'A. A. Chips',
  'date' => '2025-01-20',
  'excerpt' => 'Welcome to the Apple Chip Kitchen - where food meets philosophy, recipes meet sustainability, and every meal is an opportunity for mindful living.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kitchen\\index.md',
);

// Post content
$post_content = '<h2>Welcome to the Apple Chip Kitchen</h2>
<p>Where food meets philosophy, recipes meet sustainability, and every meal is an opportunity for mindful living.</p>
<p>The kitchen is more than just a place to prepare food - it\'s a laboratory for creativity, a sanctuary for nourishment, and a bridge between cultures. Here you\'ll find recipes, food philosophy, and thoughts on how what we eat connects us to the world around us.</p>
<h3>What You\'ll Find Here</h3>
<p>- <strong>Recipes</strong>: From simple comfort foods to cultural explorations
- <strong>Food Philosophy</strong>: Thoughts on ethics, sustainability, and mindful eating
- <strong>Cultural Connections</strong>: How food brings us together across boundaries
- <strong>Practical Tips</strong>: Making good food accessible and affordable</p>
<p>Whether you\'re looking for a new recipe to try, exploring the ethics of food choices, or just curious about the stories behind our meals, welcome to the kitchen. Pull up a chair, grab a snack, and let\'s explore the wonderful world of food together.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>