<?php
// Auto-generated blog post
// Source: content\choose_your_own_adventure_structure.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Choose Your Own Adventure Structure  Core Concept The personal blog will be structured as a  choose-your-own-adventure narrative, allowing visitors to...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Choose Your Own Adventure Structure  Core Concept The personal blog will be structured as a  choose-your-own-adventure narrative, allowing visitors to...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\choose_your_own_adventure_structure.md',
);

// Post content
$post_content = '<h1>Choose Your Own Adventure Structure</h1>
<h2>Core Concept</h2>
<p>The personal blog will be structured as a</p>
<p>choose-your-own-adventure narrative, allowing visitors to explore different facets of the user\'s life and experiences based on their interests. The goal is to provide a dynamic and personalized journey through the user\'s \'Markdown brain,\' fostering deeper understanding and connection.</p>
<h2>Entry Points (Initial Choices for the Visitor)</h2>
<p>Visitors will be presented with several overarching themes or questions that serve as their initial entry points into the narrative. These will be prominently displayed on a central \'Welcome\' or \'About Me\' page.</p>
<p>1.  <strong>"I want to understand your journey of resilience and survival."</strong>
    <em>   </em>Leads to:* Stories of homelessness, TBI recovery, sobriety, and overcoming adversity.
2.  <strong>"I\'m curious about your identity and how you see the world."</strong>
    <em>   </em>Leads to:* Content on neurodivergence (Alexithymia, autism spectrum), cultural heritage (Sephardic Jewish, Ladino), and personal philosophy.
3.  <strong>"Tell me about your community involvement and how you connect with others."</strong>
    <em>   </em>Leads to:* Experiences at 12 Baskets Café, street advocacy, teaching, and building meaningful relationships.
4.  <strong>"Show me your creative side and how you innovate."</strong>
    <em>   </em>Leads to:* Apple Chips business, Heartwarmers project, cooking, and humor/creative writing.
5.  <strong>"I\'m looking for practical advice or resources related to your experiences."</strong>
    <em>   </em>Leads to:* Guides on minimalist living, accessibility tools, advocacy resources, and coping strategies.</p>
<h2>Narrative Pathways (Branching Choices within the Story)</h2>
<p>Once a visitor selects an entry point, they will be guided through a series of interconnected posts. Each post will conclude with questions or prompts that offer new choices, leading them down different narrative paths. These choices will be presented as internal hyperlinks.</p>
<h3>Example Pathway: "Journey of Resilience and Survival"</h3>
<p>*   <strong>Start:</strong> "Healing Homelessness" (initial story of leaving home)
    <em>   </em>Choice 1:* "How did you manage daily life while living in your car?" → "Gift of Van Life" (practicalities, challenges, joys)
    <em>   </em>Choice 2:* "What was the emotional toll of that period?" → "Sobriety" (coping mechanisms, finding clarity)
    <em>   </em>Choice 3:* "How did you find community during this time?" → "Founding Patron at 12 Baskets Café" (connection, support)</p>
<h3>Example Pathway: "Identity and How You See the World"</h3>
<p>*   <strong>Start:</strong> "How Are You?" (introduction to Alexithymia and emotional processing)
    <em>   </em>Choice 1:* "How does neurodivergence impact your daily interactions?" → "Nothing About Me Without Me" (disability advocacy, microaggressions)
    <em>   </em>Choice 2:* "Tell me more about your cultural background." → (Future content on Sephardic heritage, Ladino)
    <em>   </em>Choice 3:* "What are your core beliefs and philosophies?" → "Forty Rules of Love" (spiritual influences)</p>
<h2>Missing Elements and Questions (Prompts for User Content Creation)</h2>
<p>Within each narrative pathway, there will be designated sections or prompts for content that is currently missing but crucial for a complete "choose your own adventure" experience. These will be framed as questions for the user to address in future posts.</p>
<h3>Example: Within "Journey of Resilience and Survival" → "Gift of Van Life"</h3>
<p>*   <strong>Missing Element:</strong> Detailed daily routines and challenges of van life.
    <em>   </em>Question for User:* "What was a typical day like for you living in your van? What were the most unexpected challenges and joys?"</p>
<h3>Example: Within "Identity and How You See the World" → (Future content on Sephardic heritage)</h3>
<p>*   <strong>Missing Element:</strong> Personal stories and experiences related to Sephardic identity.
    <em>   </em>Question for User:* "How has your Sephardic heritage shaped your worldview and personal identity? What traditions or stories are most meaningful to you?"</p>
<h2>Internal Hyperlinks and Indexed Table of Contents</h2>
<p>*   <strong>Internal Hyperlinks:</strong> All choices and cross-references within the narrative will be implemented as internal Markdown links (`<a href="#section-id" class="external-link">Link Text</a>` or `<a href="filename.md" class="external-link">Link Text</a>` for separate files). This allows for seamless navigation.
*   <strong>Indexed Table of Contents:</strong> A master `index.md` or `table_of_contents.md` file will serve as the central hub. It will list all available posts, categorized by theme, with clear hyperlinks. This will also include a section for "Unwritten Stories" or "Questions for Reflection" that point to the identified content gaps.</p>
<h2>User Experience Considerations</h2>
<p>*   <strong>Clear Signposting:</strong> Each post will clearly indicate where the reader is in the narrative and offer explicit choices for their next step.
*   <strong>Looping and Dead Ends:</strong> While a true "choose your own adventure" can have dead ends, for a personal blog, it\'s more about guiding the reader through interconnected themes. Loops back to central hubs or related topics will be encouraged.
*   <strong>Reflection Prompts:</strong> Interspersed throughout the content, there will be prompts for the reader to reflect on their own experiences, fostering empathy and self-discovery.
*   <strong>Call to Action for User Input:</strong> Explicitly ask the user to provide content for the identified missing elements, making the creation of the blog a collaborative process. This can be done through dedicated sections in the master document or as comments within the outline.</p>
<p>This structure aims to transform the user\'s blog from a collection of individual posts into an immersive, interactive narrative that invites visitors to explore the depths of their experiences and insights. It also provides a clear roadmap for future content creation, ensuring a cohesive and engaging experience.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>