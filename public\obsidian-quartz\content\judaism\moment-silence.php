<?php
// Auto-generated blog post
// Source: content\judaism\moment-silence.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = '<PERSON>ps://www.facebook.com/emmanuel.ortiz.16940, September 11th, 2002    Before I start this poem, I\'d like to ask you to join me In a mom...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Emmanuel Ortizhttps://www.facebook.com/emmanuel.ortiz.16940, September 11th, 2002    Before I start this poem, I\'d like to ask you to join me In a mom...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\judaism\\moment-silence.md',
);

// Post content
$post_content = '
<p><a href="https://www.facebook.com/emmanuel.ortiz.16940" class="external-link">Emmanuel Ortiz</a>, September 11th, 2002</p>

<p>Before I start this poem, I\'d like to ask you to join me</p>
<p>In a moment of silence</p>
<p>In honor of those who died in the World Trade Center and the</p>
<p>Pentagon last September 11th.</p>
<p>I would also like to ask you</p>
<p>To offer up a moment of silence</p>
<p>For all of those who have been harassed, imprisoned,</p>
<p>disappeared, tortured, raped, or killed in retaliation for those strikes,</p>
<p>For the victims in both Afghanistan and the U.S.</p>

<p>And if I could just add one more thing...</p>
<p>A full day of silence</p>
<p>For the tens of thousands of Palestinians who have died at the</p>
<p>hands of U.S.-backed Israeli</p>
<p>forces over decades of occupation.</p>
<p>Six months of silence for the million and-a-half Iraqi people,</p>
<p>mostly children, who have died of</p>
<p>malnourishment or starvation as a result of an 11-year U.S.</p>
<p>embargo against the country.</p>

<p>Before I begin this poem,</p>
<p>Two months of silence for the Blacks under Apartheid in South Africa,</p>
<p>Where homeland security made them aliens in their own country.</p>
<p>Nine months of silence for the dead in Hiroshima and Nagasaki,</p>
<p>Where death rained down and peeled back every layer of</p>
<p>concrete, steel, earth and skin</p>
<p>And the survivors went on as if alive.</p>
<p>A year of silence for the millions of dead in Vietnam - a people,</p>
<p>not a war - for those who</p>
<p>know a thing or two about the scent of burning fuel, their</p>
<p>relatives\' bones buried in it, their babies born of it.</p>
<p>A year of silence for the dead in Cambodia and Laos, victims of</p>
<p>a secret war ... ssssshhhhh....</p>
<p>Say nothing ... we don\'t want them to learn that they are dead.</p>
<p>Two months of silence for the decades of dead in Colombia,</p>
<p>Whose names, like the corpses they once represented, have</p>
<p>piled up and slipped off our tongues.</p>

<p>Before I begin this poem.</p>
<p>An hour of silence for El Salvador ...</p>
<p>An afternoon of silence for Nicaragua ...</p>
<p>Two days of silence for the Guatemaltecos ...</p>
<p>None of whom ever knew a moment of peace in their living years.</p>
<p>45 seconds of silence for the 45 dead at Acteal, Chiapas</p>
<p>25 years of silence for the hundred million Africans who found</p>
<p>their graves far deeper in the ocean than any building could</p>
<p>poke into the sky.</p>
<p>There will be no DNA testing or dental records to identify their remains.</p>
<p>And for those who were strung and swung from the heights of</p>
<p>sycamore trees in the south, the north, the east, and the west...</p>

<p>100 years of silence...</p>
<p>For the hundreds of millions of indigenous peoples from this half</p>
<p>of right here,</p>
<p>Whose land and lives were stolen,</p>
<p>In postcard-perfect plots like Pine Ridge, Wounded Knee, Sand</p>
<p>Creek,</p>
<p>Fallen Timbers, or the Trail of Tears.</p>
<p>Names now reduced to innocuous magnetic poetry on the</p>
<p>refrigerator of our consciousness ...</p>

<p>So you want a moment of silence?</p>
<p>And we are all left speechless</p>
<p>Our tongues snatched from our mouths</p>
<p>Our eyes stapled shut</p>
<p>A moment of silence</p>
<p>And the poets have all been laid to rest</p>
<p>The drums disintegrating into dust.</p>

<p>Before I begin this poem,</p>
<p>You want a moment of silence</p>
<p>You mourn now as if the world will never be the same</p>
<p>And the rest of us hope to hell it won\'t be. Not like it always has</p>
<p>been.</p>

<p>Because this is not a 9/11 poem.</p>
<p>This is a 9/10 poem,</p>
<p>It is a 9/9 poem,</p>
<p>A 9/8 poem,</p>
<p>A 9/7 poem</p>
<p>This is a 1492 poem.</p>

<p>This is a poem about what causes poems like this to be written.</p>
<p>And if this is a 9/11 poem, then:</p>
<p>This is a September 11th poem for Chile, 1971.</p>
<p>This is a September 12th poem for Steven Biko in South Africa,</p>
<p>1977.</p>
<p>This is a September 13th poem for the brothers at Attica Prison,</p>
<p>New York, 1971.</p>
<p>This is a September 14th poem for Somalia, 1992.</p>
<p>This is a poem for every date that falls to the ground in ashes</p>
<p>This is a poem for the 110 stories that were never told</p>
<p>The 110 stories that history chose not to write in textbooks</p>
<p>The 110 stories that CNN, BBC, The New York Times, and</p>
<p>Newsweek ignored.</p>
<p>This is a poem for interrupting this program.</p>

<p>And still you want a moment of silence for your dead?</p>
<p>We could give you lifetimes of empty:</p>
<p>The unmarked graves</p>
<p>The lost languages</p>
<p>The uprooted trees and histories</p>
<p>The dead stares on the faces of nameless children</p>
<p>Before I start this poem we could be silent forever</p>
<p>Or just long enough to hunger,</p>
<p>For the dust to bury us</p>
<p>And you would still ask us</p>
<p>For more of our silence.</p>

<p>If you want a moment of silence</p>
<p>Then stop the oil pumps</p>
<p>Turn off the engines and the televisions</p>
<p>Sink the cruise ships</p>
<p>Crash the stock markets</p>
<p>Unplug the marquee lights,</p>
<p>Delete the instant messages,</p>
<p>Derail the trains, the light rail transit.</p>

<p>If you want a moment of silence, put a brick through the window</p>
<p>of Taco Bell,</p>
<p>And pay the workers for wages lost.</p>
<p>Tear down the liquor stores,</p>
<p>The townhouses, the White Houses, the jailhouses, the</p>
<p>Penthouses and the Playboys.</p>

<p>If you want a moment of silence,</p>
<p>Then take it</p>
<p>On Super Bowl Sunday,</p>
<p>The Fourth of July</p>
<p>During Dayton\'s 13 hour sale</p>
<p>Or the next time your white guilt fills the room where my beautiful</p>
<p>people have gathered.</p>

<p>You want a moment of silence</p>
<p>Then take it NOW,</p>
<p>Before this poem begins.</p>
<p>Here, in the echo of my voice,</p>
<p>In the pause between goosesteps of the second hand,</p>
<p>In the space between bodies in embrace,</p>
<p>Here is your silence.</p>
<p>Take it.</p>
<p>But take it all...Don\'t cut in line.</p>
<p>Let your silence begin at the beginning of crime. But we,</p>
<p>Tonight we will keep right on singing...For our dead.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>