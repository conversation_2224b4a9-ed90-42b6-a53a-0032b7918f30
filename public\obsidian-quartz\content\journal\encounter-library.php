<?php
// Auto-generated blog post
// Source: content\journal\encounter-library.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Encounter at the Public Library';
$meta_description = 'A woman approached me in the parking lot of the public library, recognizing me from Haywood Church. She expressed her displeasure with my t-shirt, which read ‘No Human Being is Illegal.’ Her anger stemmed from her belief that taxes were being used to support undocumented immigrants with food, housing, and medical care. She challenged me, asking if I would personally house and care for these individuals.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Encounter at the Public Library',
  'author' => 'A. A. Chips',
  'date' => '2017-07-24',
  'excerpt' => 'A woman approached me in the parking lot of the public library, recognizing me from Haywood Church. She expressed her displeasure with my t-shirt, which read ‘No Human Being is Illegal.’ Her anger stemmed from her belief that taxes were being used to support undocumented immigrants with food, housing, and medical care. She challenged me, asking if I would personally house and care for these individuals.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\encounter-library.md',
);

// Post content
$post_content = '<p>A woman approached me in the parking lot of the public library, recognizing me from Haywood Church. She expressed her displeasure with my t-shirt, which read ‘No Human Being is Illegal.’ Her anger stemmed from her belief that taxes were being used to support undocumented immigrants with food, housing, and medical care. She challenged me, asking if I would personally house and care for these individuals.</p>
<p>I responded that I would, provided she agreed to the United States ceasing its corporate, manufacturing, and military interventions in these immigrants\' countries of origin. This immediately revealed our shared perspective. Her primary concern was not the legal status of people but rather the broader issue of colonization. She shared her own experiences of being told to "go back to Europe" in Arizona and being reminded that the land was "Mexican territory."</p>
<p>We found common ground in our opposition to all forms of colonization and the detrimental impact of the US government and corporations\' practices on future generations. Despite the contentious beginning, we became friends. While this interaction didn\'t tangibly stop colonial violence, it highlighted a shared understanding of its underlying causes.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>